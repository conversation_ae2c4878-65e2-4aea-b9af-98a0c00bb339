import { render, screen } from '@testing-library/react';
import { LoadingSpinner, LoadingState, InlineLoading, ButtonLoading } from '../../src/components/ui/LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<LoadingSpinner size="sm" />);
    expect(document.querySelector('.h-4')).toBeInTheDocument();

    rerender(<LoadingSpinner size="lg" />);
    expect(document.querySelector('.h-12')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<LoadingSpinner className="custom-class" />);
    expect(document.querySelector('.custom-class')).toBeInTheDocument();
  });
});

describe('LoadingState', () => {
  it('renders with default message', () => {
    render(<LoadingState />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(<LoadingState message="Please wait..." />);
    expect(screen.getByText('Please wait...')).toBeInTheDocument();
  });
});

describe('InlineLoading', () => {
  it('renders inline loading component', () => {
    render(<InlineLoading />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(<InlineLoading message="Processing..." />);
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });
});

describe('ButtonLoading', () => {
  it('renders children when not loading', () => {
    render(
      <ButtonLoading isLoading={false}>
        Click me
      </ButtonLoading>
    );
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    render(
      <ButtonLoading isLoading={true}>
        Click me
      </ButtonLoading>
    );
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows custom loading text', () => {
    render(
      <ButtonLoading isLoading={true} loadingText="Saving...">
        Save
      </ButtonLoading>
    );
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  it('is disabled when loading', () => {
    render(
      <ButtonLoading isLoading={true}>
        Click me
      </ButtonLoading>
    );
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('is disabled when disabled prop is true', () => {
    render(
      <ButtonLoading isLoading={false} disabled={true}>
        Click me
      </ButtonLoading>
    );
    expect(screen.getByRole('button')).toBeDisabled();
  });
});

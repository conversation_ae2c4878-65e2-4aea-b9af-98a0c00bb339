/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';
import { POST } from '../../src/app/api/campaigns/route';

// Mock the database
jest.mock('@db/client', () => ({
  db: {
    insert: jest.fn().mockReturnValue({
      values: jest.fn().mockReturnValue({
        returning: jest.fn().mockResolvedValue([{
          id: 1,
          workspaceId: 1,
          name: 'Test Campaign',
          objective: 'awareness',
          status: 'draft',
          createdAt: new Date(),
          updatedAt: new Date()
        }])
      })
    }),
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([{
            id: 1,
            name: 'Test Workspace',
            slug: 'test'
          }])
        })
      })
    })
  }
}));

jest.mock('@db/schema', () => ({
  campaigns: {},
  workspaces: {}
}));

describe('/api/campaigns', () => {
  describe('POST', () => {
    it('should create a campaign successfully', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test Campaign',
          workspaceSlug: 'test',
          objective: 'awareness'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toHaveProperty('id');
      expect(data.name).toBe('Test Campaign');
      expect(data.objective).toBe('awareness');
    });

    it('should return 400 for missing required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        body: JSON.stringify({
          // Missing name and workspaceSlug
          objective: 'awareness'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
    });

    it('should return 400 for invalid JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
    });

    it('should handle database errors gracefully', async () => {
      // Mock database error
      const { db } = require('@db/client');
      db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test Campaign',
          workspaceSlug: 'test',
          objective: 'awareness'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toHaveProperty('error');
    });
  });
});

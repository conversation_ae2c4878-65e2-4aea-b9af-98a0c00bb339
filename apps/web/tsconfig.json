{"extends": "../../tsconfig.base.json", "compilerOptions": {"types": ["node"], "baseUrl": ".", "paths": {"@shared/*": ["../../packages/shared/src/*"], "@db/*": ["../../packages/db/src/*"], "@agents/*": ["../../packages/agents/src/*"]}, "esModuleInterop": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
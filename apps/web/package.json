{"name": "@ugc/web", "version": "0.1.0", "private": true, "license": "UNLICENSED", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc -p tsconfig.json --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "ui": "shadcn"}, "dependencies": {"@ugc/db": "workspace:*", "drizzle-orm": "^0.44.4", "pg": "^8.16.3", "zod": "^4.0.14"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "type": "module"}
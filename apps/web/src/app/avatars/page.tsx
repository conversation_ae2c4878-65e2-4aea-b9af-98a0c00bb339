'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { LoadingState } from '../../components/ui/LoadingSpinner';
import { ErrorState } from '../../components/ui/ErrorState';

interface Avatar {
  id: number;
  name: string;
  description: string;
  thumbnailUrl: string;
  videoSampleUrl?: string;
  voiceSampleUrl?: string;
  demographics: any;
  personality: any;
  languages: string[];
  contentTypes: string[];
  platforms: string[];
  tags: string[];
  status: string;
  creator: {
    id: number;
    name: string;
    profileImageUrl: string;
  };
  license?: {
    id: number;
    licenseType: string;
    pricing: any;
    status: string;
  };
}

interface AvatarsResponse {
  avatars: Avatar[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: any;
}

export default function AvatarsPage() {
  const [avatars, setAvatars] = useState<Avatar[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    search: '',
    language: '',
    demographic: '',
    platform: '',
    contentType: ''
  });
  const [selectedAvatar, setSelectedAvatar] = useState<Avatar | null>(null);

  const fetchAvatars = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        workspace: 'acme',
        limit: '20',
        ...(filters.search && { search: filters.search }),
        ...(filters.language && { language: filters.language }),
        ...(filters.demographic && { demographic: filters.demographic }),
        ...(filters.platform && { platform: filters.platform }),
        ...(filters.contentType && { contentType: filters.contentType }),
      });

      const response = await fetch(`/api/avatars?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch avatars');
      }

      const data: AvatarsResponse = await response.json();
      setAvatars(data.avatars);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAvatars();
  }, [filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSelectAvatar = (avatar: Avatar) => {
    setSelectedAvatar(avatar);
  };

  if (loading) {
    return <LoadingState message="Loading avatar catalog..." />;
  }

  if (error) {
    return (
      <ErrorState
        message={error}
        action={{
          label: 'Retry',
          onClick: fetchAvatars
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Avatar Catalog</span>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Licensed Avatar Catalog</h1>
          <p className="text-gray-600">
            Choose from our curated collection of licensed creators for your UGC campaigns.
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Avatars</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search avatars..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
              <select
                value={filters.language}
                onChange={(e) => handleFilterChange('language', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Languages</option>
                <option value="english">English</option>
                <option value="spanish">Spanish</option>
                <option value="mandarin">Mandarin</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Demographics</label>
              <select
                value={filters.demographic}
                onChange={(e) => handleFilterChange('demographic', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Demographics</option>
                <option value="female">Female</option>
                <option value="male">Male</option>
                <option value="hispanic">Hispanic</option>
                <option value="asian">Asian</option>
                <option value="black">Black</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Platform</label>
              <select
                value={filters.platform}
                onChange={(e) => handleFilterChange('platform', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Platforms</option>
                <option value="tiktok">TikTok</option>
                <option value="instagram">Instagram</option>
                <option value="youtube">YouTube</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
              <select
                value={filters.contentType}
                onChange={(e) => handleFilterChange('contentType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                <option value="review">Review</option>
                <option value="tutorial">Tutorial</option>
                <option value="unboxing">Unboxing</option>
                <option value="routine">Routine</option>
                <option value="fashion">Fashion</option>
                <option value="fitness">Fitness</option>
              </select>
            </div>
          </div>
        </div>

        {/* Avatar Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {avatars.map((avatar) => (
            <div
              key={avatar.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleSelectAvatar(avatar)}
            >
              {/* Avatar Thumbnail */}
              <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                <img
                  src={avatar.thumbnailUrl}
                  alt={avatar.name}
                  className="w-full h-48 object-cover"
                />
              </div>
              
              {/* Avatar Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">{avatar.name}</h3>
                  {avatar.license && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                      Licensed
                    </span>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{avatar.description}</p>
                
                {/* Creator Info */}
                <div className="flex items-center mb-3">
                  <img
                    src={avatar.creator.profileImageUrl}
                    alt={avatar.creator.name}
                    className="w-6 h-6 rounded-full mr-2"
                  />
                  <span className="text-sm text-gray-700">{avatar.creator.name}</span>
                </div>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {avatar.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                  {avatar.tags.length > 3 && (
                    <span className="text-xs text-gray-500">+{avatar.tags.length - 3} more</span>
                  )}
                </div>
                
                {/* Pricing */}
                {avatar.license && (
                  <div className="text-sm text-gray-900 font-medium">
                    ${avatar.license.pricing.perVideo}/video
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {avatars.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">👤</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No avatars found</h3>
            <p className="text-gray-600">Try adjusting your filters to see more results.</p>
          </div>
        )}
      </div>

      {/* Avatar Detail Modal */}
      {selectedAvatar && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <h2 className="text-2xl font-bold text-gray-900">{selectedAvatar.name}</h2>
                <button
                  onClick={() => setSelectedAvatar(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <img
                    src={selectedAvatar.thumbnailUrl}
                    alt={selectedAvatar.name}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                    <p className="text-gray-600">{selectedAvatar.description}</p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Creator</h3>
                    <div className="flex items-center">
                      <img
                        src={selectedAvatar.creator.profileImageUrl}
                        alt={selectedAvatar.creator.name}
                        className="w-8 h-8 rounded-full mr-3"
                      />
                      <span className="text-gray-700">{selectedAvatar.creator.name}</span>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Languages</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedAvatar.languages.map((lang) => (
                        <span key={lang} className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
                          {lang}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Content Types</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedAvatar.contentTypes.map((type) => (
                        <span key={type} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {selectedAvatar.license && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Pricing</h3>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="text-sm text-green-800">
                          <div>Setup: ${selectedAvatar.license.pricing.setup}</div>
                          <div>Per Video: ${selectedAvatar.license.pricing.perVideo}</div>
                          <div>Monthly: ${selectedAvatar.license.pricing.monthly}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setSelectedAvatar(null)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Close
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Select Avatar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

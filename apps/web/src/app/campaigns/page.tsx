'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function CampaignsPage() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCampaign, setGeneratedCampaign] = useState<any>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const generateCampaign = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setMessage(null);
    setGeneratedCampaign(null);

    try {
      // Simulate AI processing with realistic campaign generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Parse the prompt to extract campaign details
      const campaignData = parseCampaignPrompt(prompt);
      setGeneratedCampaign(campaignData);
      setMessage({ type: 'success', text: 'Campaign generated! Review and create if it looks good.' });
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to generate campaign' });
    } finally {
      setIsGenerating(false);
    }
  };

  const createCampaign = async () => {
    if (!generatedCampaign) return;

    setIsCreating(true);
    setMessage(null);

    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: generatedCampaign.name,
          workspaceSlug: 'acme',
          objective: generatedCampaign.objective
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: `Campaign "${result.name}" created successfully!` });
        setPrompt('');
        setGeneratedCampaign(null);
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to create campaign' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsCreating(false);
    }
  };

  const parseCampaignPrompt = (prompt: string): any => {
    // Simple AI-like parsing logic (in real implementation, this would call an LLM)
    const lowerPrompt = prompt.toLowerCase();

    let objective = 'awareness';
    if (lowerPrompt.includes('convert') || lowerPrompt.includes('sale') || lowerPrompt.includes('purchase')) {
      objective = 'conversion';
    } else if (lowerPrompt.includes('engage') || lowerPrompt.includes('interact') || lowerPrompt.includes('comment')) {
      objective = 'engagement';
    }

    // Extract potential campaign name
    let name = 'AI Generated Campaign';
    if (lowerPrompt.includes('launch')) {
      name = 'Product Launch Campaign';
    } else if (lowerPrompt.includes('summer') || lowerPrompt.includes('seasonal')) {
      name = 'Summer Campaign';
    } else if (lowerPrompt.includes('brand')) {
      name = 'Brand Awareness Campaign';
    }

    return {
      name,
      objective,
      description: prompt,
      suggestedChannels: ['tiktok', 'instagram'],
      targetAudience: 'Young adults 18-35',
      keyMessages: ['Authentic', 'Relatable', 'Trendy']
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Campaigns</span>
            <span className="text-gray-400">→</span>
            <Link href="/briefs" className="text-blue-600 hover:text-blue-800">
              Briefs
            </Link>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Campaign with AI</h1>
          <p className="text-gray-600">
            Describe your campaign goals and let AI generate a comprehensive campaign strategy.
          </p>
        </div>

        {/* AI Prompt Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                Describe your campaign 💬
              </label>
              <textarea
                id="prompt"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., I want to launch a summer campaign for our new skincare line targeting young women. The goal is to drive awareness and showcase how our products fit into a daily routine. I want authentic, relatable content that feels natural and not overly promotional..."
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                💡 Be specific about your product, target audience, goals, and tone
              </div>
              <button
                onClick={generateCampaign}
                disabled={isGenerating || !prompt.trim()}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </span>
                ) : 'Generate Campaign ✨'}
              </button>
            </div>
          </div>
        </div>

        {/* Generated Campaign Preview */}
        {generatedCampaign && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Generated Campaign</h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => setGeneratedCampaign(null)}
                  className="text-gray-500 hover:text-gray-700 px-3 py-1 text-sm"
                >
                  Regenerate
                </button>
                <button
                  onClick={createCampaign}
                  disabled={isCreating}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  {isCreating ? 'Creating...' : 'Create Campaign'}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Campaign Details</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Name:</span> {generatedCampaign.name}</div>
                  <div><span className="font-medium">Objective:</span> {generatedCampaign.objective}</div>
                  <div><span className="font-medium">Target Audience:</span> {generatedCampaign.targetAudience}</div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Strategy</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Channels:</span>
                    <div className="flex space-x-2 mt-1">
                      {generatedCampaign.suggestedChannels.map((channel: string) => (
                        <span key={channel} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                          {channel}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Key Messages:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {generatedCampaign.keyMessages.map((message: string) => (
                        <span key={message} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                          {message}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Message Display */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success'
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}

        {/* Example Prompts */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-900 mb-3">Example Prompts</h3>
          <div className="space-y-2">
            <button
              onClick={() => setPrompt("I want to launch a summer campaign for our new skincare line targeting young women. The goal is to drive awareness and showcase how our products fit into a daily routine. I want authentic, relatable content that feels natural and not overly promotional.")}
              className="text-left w-full p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              💄 Skincare product launch for young women
            </button>
            <button
              onClick={() => setPrompt("Create a campaign to drive conversions for our fitness app. Target busy professionals who want quick workouts. Focus on convenience, results, and time-saving. The tone should be motivational but realistic.")}
              className="text-left w-full p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              💪 Fitness app conversion campaign
            </button>
            <button
              onClick={() => setPrompt("I need a brand awareness campaign for our sustainable fashion brand. Target environmentally conscious millennials. Emphasize our eco-friendly materials and ethical production. The content should feel authentic and educational.")}
              className="text-left w-full p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              🌱 Sustainable fashion awareness
            </button>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">How it works</h3>
          <div className="text-sm text-gray-700 space-y-1">
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">1</span>
              Describe your campaign goals, target audience, and desired tone
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">2</span>
              AI analyzes your prompt and generates a comprehensive campaign strategy
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">3</span>
              Review and create the campaign, then head to{' '}
              <Link href="/briefs" className="text-blue-600 underline hover:no-underline ml-1">
                Briefs
              </Link>{' '}
              to generate content
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

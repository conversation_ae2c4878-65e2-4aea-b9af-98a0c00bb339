'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function ApiDocsPage() {
  const [spec, setSpec] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // In a real implementation, you'd fetch the OpenAPI spec
    // For now, we'll show a placeholder
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading API documentation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Documentation</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">API Documentation</h1>
              <p className="text-gray-600 mt-1">UGC Orchestrator REST API Reference</p>
            </div>
            <Link
              href="/"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Back to App
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Endpoints</h3>
              <nav className="space-y-2">
                <a href="#campaigns" className="block text-blue-600 hover:text-blue-800 text-sm">
                  POST /campaigns
                </a>
                <a href="#briefs" className="block text-blue-600 hover:text-blue-800 text-sm">
                  POST /briefs
                </a>
                <a href="#approve" className="block text-blue-600 hover:text-blue-800 text-sm">
                  POST /briefs/:id/approve
                </a>
                <a href="#events" className="block text-blue-600 hover:text-blue-800 text-sm">
                  GET /events/stream
                </a>
              </nav>
              
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Resources</h4>
                <div className="space-y-1">
                  <a 
                    href="/docs/api-spec.yaml" 
                    target="_blank"
                    className="block text-sm text-blue-600 hover:text-blue-800"
                  >
                    📄 OpenAPI Spec
                  </a>
                  <a 
                    href="https://swagger.io/tools/swagger-ui/" 
                    target="_blank"
                    className="block text-sm text-blue-600 hover:text-blue-800"
                  >
                    🔧 Swagger UI
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Overview */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
              <p className="text-gray-600 mb-4">
                The UGC Orchestrator API enables AI-powered campaign creation, content brief generation, 
                and approval workflows. All endpoints accept and return JSON data.
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Base URL</h3>
                <code className="text-sm text-blue-800">http://localhost:3002/api</code>
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Authentication</h3>
                <p className="text-sm text-gray-600">
                  Currently in development mode. Production will require JWT bearer tokens.
                </p>
              </div>
            </div>

            {/* Campaigns Endpoint */}
            <div id="campaigns" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Create Campaign</h2>
              <div className="flex items-center space-x-2 mb-4">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium">POST</span>
                <code className="text-sm text-gray-800">/campaigns</code>
              </div>
              
              <p className="text-gray-600 mb-4">
                Creates a new UGC campaign with AI-powered strategy generation. 
                Accepts natural language descriptions and generates structured campaign data.
              </p>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Request Body</h3>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <pre className="text-sm text-gray-800 overflow-x-auto">
{`{
  "name": "Summer Product Launch",
  "workspaceSlug": "acme",
  "objective": "awareness",
  "prompt": "I want to launch a summer campaign..."
}`}
                </pre>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Response</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <pre className="text-sm text-gray-800 overflow-x-auto">
{`{
  "id": 1,
  "workspaceId": 1,
  "name": "Summer Product Launch",
  "objective": "awareness",
  "status": "draft",
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}`}
                </pre>
              </div>
            </div>

            {/* Briefs Endpoint */}
            <div id="briefs" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Generate Brief</h2>
              <div className="flex items-center space-x-2 mb-4">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium">POST</span>
                <code className="text-sm text-gray-800">/briefs</code>
              </div>
              
              <p className="text-gray-600 mb-4">
                Generates AI-powered content briefs based on natural language prompts. 
                Creates detailed scripts, angles, and captions for content creators.
              </p>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Request Body</h3>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <pre className="text-sm text-gray-800 overflow-x-auto">
{`{
  "campaignId": 1,
  "title": "Morning Skincare Routine",
  "prompt": "Create a TikTok video showing a morning skincare routine..."
}`}
                </pre>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Response</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <pre className="text-sm text-gray-800 overflow-x-auto">
{`{
  "id": 1,
  "campaignId": 1,
  "title": "Morning Skincare Routine",
  "content": {
    "angles": [...],
    "scripts": [...],
    "captions": [...]
  },
  "generatedBy": "agent",
  "createdAt": "2024-01-01T12:00:00Z"
}`}
                </pre>
              </div>
            </div>

            {/* Approval Endpoint */}
            <div id="approve" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Approve Brief</h2>
              <div className="flex items-center space-x-2 mb-4">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium">POST</span>
                <code className="text-sm text-gray-800">/briefs/:id/approve</code>
              </div>
              
              <p className="text-gray-600 mb-4">
                Records approval or rejection status for a generated brief. 
                Includes optional notes and tracks the deciding user.
              </p>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Request Body</h3>
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <pre className="text-sm text-gray-800 overflow-x-auto">
{`{
  "status": "approved",
  "note": "Looks great, ready for production",
  "userId": 1
}`}
                </pre>
              </div>
            </div>

            {/* Events Endpoint */}
            <div id="events" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Event Stream</h2>
              <div className="flex items-center space-x-2 mb-4">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">GET</span>
                <code className="text-sm text-gray-800">/events/stream</code>
              </div>
              
              <p className="text-gray-600 mb-4">
                Establishes a Server-Sent Events connection for real-time updates. 
                Provides heartbeat and event notifications for brief generation and approvals.
              </p>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-yellow-900 mb-2">Content-Type</h3>
                <code className="text-sm text-yellow-800">text/event-stream</code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

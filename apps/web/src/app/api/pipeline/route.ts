import { NextRequest } from "next/server";
import { db } from "@db/client";
import { renderScripts, briefs, campaigns } from "@db/schema";
import { eq } from "drizzle-orm";

// Mock pipeline orchestrator for now (would be real implementation in production)
class MockPipelineOrchestrator {
  async startPipeline(
    workspaceId: number,
    briefId: number,
    renderScriptId: number,
    options?: {
      avatarId?: number;
      platforms?: string[];
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    // Generate a mock execution ID
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`Starting pipeline execution ${executionId}`);
    console.log(`Workspace: ${workspaceId}, Brief: ${briefId}, RenderScript: ${renderScriptId}`);
    console.log(`Options:`, options);
    
    // Simulate pipeline stages
    const stages = ['tts-generation', 'lip-sync', 'composition', 'upload', 'notification'];
    console.log(`Pipeline stages: ${stages.join(' → ')}`);
    
    return executionId;
  }

  async getPipelineStatus(executionId: string) {
    // Mock pipeline status
    return {
      id: executionId,
      status: 'running',
      currentStage: 'tts-generation',
      stages: [
        { stage: 'tts-generation', status: 'processing', startedAt: new Date() },
        { stage: 'lip-sync', status: 'pending' },
        { stage: 'composition', status: 'pending' },
        { stage: 'upload', status: 'pending' },
        { stage: 'notification', status: 'pending' }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}

const mockOrchestrator = new MockPipelineOrchestrator();

// POST /api/pipeline - Start a new pipeline execution
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { briefId, avatarId, platforms, metadata } = body;

    if (!briefId || !Number.isFinite(briefId)) {
      return new Response(JSON.stringify({ error: "briefId is required" }), {
        status: 400,
        headers: { "content-type": "application/json" },
      });
    }

    // Get the brief and associated data
    const [brief] = await db
      .select({
        brief: briefs,
        campaign: campaigns,
      })
      .from(briefs)
      .leftJoin(campaigns, eq(briefs.campaignId, campaigns.id))
      .where(eq(briefs.id, briefId))
      .limit(1);

    if (!brief) {
      return new Response(JSON.stringify({ error: "Brief not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    // Create a render script for this pipeline execution
    const [renderScript] = await db
      .insert(renderScripts)
      .values({
        briefId: briefId,
        avatarId: avatarId || 1, // Default avatar if not specified
        title: `Render Script for ${brief.brief.title}`,
        script: `Generated script content for brief: ${brief.brief.title}`,
        platform: platforms?.[0] || 'tiktok',
        format: 'vertical',
        status: 'approved',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    // Start the pipeline
    const executionId = await mockOrchestrator.startPipeline(
      brief.campaign?.workspaceId || 1,
      briefId,
      renderScript.id,
      {
        avatarId: avatarId || 1,
        platforms: platforms || ['tiktok'],
        metadata: {
          briefTitle: brief.brief.title,
          campaignName: brief.campaign?.name,
          ...metadata
        }
      }
    );

    return new Response(
      JSON.stringify({
        executionId,
        briefId,
        renderScriptId: renderScript.id,
        status: 'started',
        stages: ['tts-generation', 'lip-sync', 'composition', 'upload', 'notification'],
        createdAt: new Date().toISOString(),
        message: 'Pipeline execution started successfully'
      }),
      {
        status: 201,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("POST /api/pipeline error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

// GET /api/pipeline?executionId=xxx - Get pipeline status
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const executionId = searchParams.get('executionId');

    if (!executionId) {
      return new Response(JSON.stringify({ error: "executionId is required" }), {
        status: 400,
        headers: { "content-type": "application/json" },
      });
    }

    const status = await mockOrchestrator.getPipelineStatus(executionId);

    return new Response(
      JSON.stringify(status),
      {
        status: 200,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("GET /api/pipeline error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

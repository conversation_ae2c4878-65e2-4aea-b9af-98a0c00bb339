import { NextRequest } from "next/server";
import { db } from "@db/client";
import { avatars, creators, avatarLicenses, workspaces } from "@db/schema";
import { eq, and, like, inArray, sql } from "drizzle-orm";

// GET /api/avatars - Get avatars with filtering
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Extract query parameters
    const workspaceSlug = searchParams.get('workspace') || 'acme';
    const language = searchParams.get('language');
    const demographic = searchParams.get('demographic');
    const tone = searchParams.get('tone');
    const platform = searchParams.get('platform');
    const contentType = searchParams.get('contentType');
    const search = searchParams.get('search');
    const status = searchParams.get('status') || 'active';
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get workspace ID
    const [workspace] = await db
      .select()
      .from(workspaces)
      .where(eq(workspaces.slug, workspaceSlug))
      .limit(1);

    if (!workspace) {
      return new Response(JSON.stringify({ error: "Workspace not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    // Build the query with joins
    let query = db
      .select({
        id: avatars.id,
        name: avatars.name,
        description: avatars.description,
        thumbnailUrl: avatars.thumbnailUrl,
        videoSampleUrl: avatars.videoSampleUrl,
        voiceSampleUrl: avatars.voiceSampleUrl,
        demographics: avatars.demographics,
        personality: avatars.personality,
        languages: avatars.languages,
        contentTypes: avatars.contentTypes,
        platforms: avatars.platforms,
        tags: avatars.tags,
        status: avatars.status,
        createdAt: avatars.createdAt,
        creator: {
          id: creators.id,
          name: creators.name,
          profileImageUrl: creators.profileImageUrl,
        },
        license: {
          id: avatarLicenses.id,
          licenseType: avatarLicenses.licenseType,
          pricing: avatarLicenses.pricing,
          status: avatarLicenses.status,
        }
      })
      .from(avatars)
      .leftJoin(creators, eq(avatars.creatorId, creators.id))
      .leftJoin(
        avatarLicenses, 
        and(
          eq(avatarLicenses.avatarId, avatars.id),
          eq(avatarLicenses.workspaceId, workspace.id)
        )
      )
      .where(eq(avatars.status, status));

    // Apply filters
    const conditions = [eq(avatars.status, status)];

    if (search) {
      conditions.push(
        sql`(${avatars.name} ILIKE ${`%${search}%`} OR ${avatars.description} ILIKE ${`%${search}%`})`
      );
    }

    if (language) {
      conditions.push(
        sql`${avatars.languages}::text ILIKE ${`%${language}%`}`
      );
    }

    if (demographic) {
      conditions.push(
        sql`${avatars.demographics}::text ILIKE ${`%${demographic}%`}`
      );
    }

    if (tone) {
      conditions.push(
        sql`${avatars.personality}::text ILIKE ${`%${tone}%`}`
      );
    }

    if (platform) {
      conditions.push(
        sql`${avatars.platforms}::text ILIKE ${`%${platform}%`}`
      );
    }

    if (contentType) {
      conditions.push(
        sql`${avatars.contentTypes}::text ILIKE ${`%${contentType}%`}`
      );
    }

    // Apply all conditions
    if (conditions.length > 1) {
      query = query.where(and(...conditions));
    } else if (conditions.length === 1) {
      query = query.where(conditions[0]);
    }

    // Apply pagination and ordering
    const results = await query
      .orderBy(avatars.createdAt)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const [{ count }] = await db
      .select({ count: sql<number>`count(*)` })
      .from(avatars)
      .where(conditions.length > 1 ? and(...conditions) : conditions[0] || eq(avatars.status, status));

    // Process results to parse JSON fields
    const processedResults = results.map(avatar => ({
      ...avatar,
      demographics: avatar.demographics ? JSON.parse(avatar.demographics) : null,
      personality: avatar.personality ? JSON.parse(avatar.personality) : null,
      languages: avatar.languages ? JSON.parse(avatar.languages) : [],
      contentTypes: avatar.contentTypes ? JSON.parse(avatar.contentTypes) : [],
      platforms: avatar.platforms ? JSON.parse(avatar.platforms) : [],
      tags: avatar.tags ? JSON.parse(avatar.tags) : [],
      license: avatar.license?.pricing ? {
        ...avatar.license,
        pricing: JSON.parse(avatar.license.pricing)
      } : avatar.license
    }));

    return new Response(
      JSON.stringify({
        avatars: processedResults,
        pagination: {
          total: count,
          limit,
          offset,
          hasMore: offset + limit < count
        },
        filters: {
          workspace: workspaceSlug,
          language,
          demographic,
          tone,
          platform,
          contentType,
          search,
          status
        }
      }),
      {
        status: 200,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("GET /api/avatars error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

// POST /api/avatars - Create a new avatar (for admin use)
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      creatorId,
      name,
      description,
      thumbnailUrl,
      videoSampleUrl,
      voiceSampleUrl,
      demographics,
      personality,
      languages,
      contentTypes,
      platforms,
      tags
    } = body;

    if (!creatorId || !name || !thumbnailUrl) {
      return new Response(JSON.stringify({ error: "creatorId, name, and thumbnailUrl are required" }), {
        status: 400,
        headers: { "content-type": "application/json" },
      });
    }

    // Verify creator exists
    const [creator] = await db
      .select()
      .from(creators)
      .where(eq(creators.id, creatorId))
      .limit(1);

    if (!creator) {
      return new Response(JSON.stringify({ error: "Creator not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    const [avatar] = await db
      .insert(avatars)
      .values({
        creatorId,
        name,
        description,
        thumbnailUrl,
        videoSampleUrl,
        voiceSampleUrl,
        demographics: demographics ? JSON.stringify(demographics) : null,
        personality: personality ? JSON.stringify(personality) : null,
        languages: languages ? JSON.stringify(languages) : null,
        contentTypes: contentTypes ? JSON.stringify(contentTypes) : null,
        platforms: platforms ? JSON.stringify(platforms) : null,
        tags: tags ? JSON.stringify(tags) : null,
      })
      .returning();

    return new Response(
      JSON.stringify({
        id: avatar.id,
        creatorId: avatar.creatorId,
        name: avatar.name,
        description: avatar.description,
        thumbnailUrl: avatar.thumbnailUrl,
        status: avatar.status,
        createdAt: avatar.createdAt
      }),
      {
        status: 201,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("POST /api/avatars error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

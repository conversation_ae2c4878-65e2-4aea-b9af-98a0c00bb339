import { NextRequest } from "next/server";
import { db } from "@db/client";
import { briefs, campaigns } from "@db/schema";
import { eq } from "drizzle-orm";

// POST /api/briefs/generate
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const campaignId = Number(body?.campaignId);
    const title = String(body?.title ?? "").trim();
    const prompt = String(body?.prompt ?? "").trim();

    if (!campaignId || !Number.isFinite(campaignId)) {
      return new Response(JSON.stringify({ error: "campaignId is required" }), {
        status: 400,
        headers: { "content-type": "application/json" },
      });
    }

    const [camp] = await db
      .select()
      .from(campaigns)
      .where(eq(campaigns.id, campaignId))
      .limit(1);
    if (!camp) {
      return new Response(JSON.stringify({ error: "Campaign not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    // Generate content based on the prompt
    const generated = generateContentFromPrompt(prompt, title);

    // Add a realistic delay to simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 1500));

    const [created] = await db
      .insert(briefs)
      .values({
        campaignId: camp.id,
        title: title || `Auto Brief for ${camp.name}`,
        content: JSON.stringify(generated),
        generatedBy: "agent",
      })
      .returning();

    return new Response(
      JSON.stringify({
        id: created.id,
        campaignId: created.campaignId,
        title: created.title,
        content: generated,
      }),
      {
        status: 201,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("POST /api/briefs/generate error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}

function generateContentFromPrompt(prompt: string, title: string) {
  const lowerPrompt = prompt.toLowerCase();

  // Determine platform
  let platform = 'tiktok';
  if (lowerPrompt.includes('instagram')) platform = 'instagram';
  if (lowerPrompt.includes('youtube')) platform = 'youtube';

  // Determine content type
  let contentType = 'general';
  if (lowerPrompt.includes('unboxing')) contentType = 'unboxing';
  if (lowerPrompt.includes('tutorial') || lowerPrompt.includes('how to')) contentType = 'tutorial';
  if (lowerPrompt.includes('review')) contentType = 'review';
  if (lowerPrompt.includes('routine') || lowerPrompt.includes('day in the life')) contentType = 'routine';
  if (lowerPrompt.includes('skincare')) contentType = 'skincare';

  // Generate content based on type
  const contentTemplates = {
    unboxing: {
      angles: [
        { id: "a1", hook: "Unboxing Excitement", promise: "Show genuine reaction to receiving package" },
        { id: "a2", hook: "First Impressions", promise: "Immediate thoughts on packaging and products" }
      ],
      scripts: [
        {
          id: "s1", angleId: "a1",
          lines: [
            "OMG you guys, my package finally arrived!",
            "Let's see what's inside... *excited unwrapping*",
            "Wow, the packaging is so cute!",
            "I can't wait to try this!"
          ]
        }
      ],
      captions: [
        { id: "c1", text: "Unboxing my new favorites! 📦✨ #unboxing #newproducts", platform }
      ]
    },
    skincare: {
      angles: [
        { id: "a1", hook: "Morning Routine", promise: "Show step-by-step skincare routine" },
        { id: "a2", hook: "Before/After", promise: "Demonstrate visible results" }
      ],
      scripts: [
        {
          id: "s1", angleId: "a1",
          lines: [
            "Starting my morning with this amazing serum",
            "Just a few drops on clean skin",
            "Look at how it absorbs so quickly!",
            "My skin feels so smooth and hydrated"
          ]
        }
      ],
      captions: [
        { id: "c1", text: "Morning glow routine ✨ This serum is a game changer! #skincare #glowup", platform }
      ]
    },
    routine: {
      angles: [
        { id: "a1", hook: "Day in the Life", promise: "Show authentic daily usage" },
        { id: "a2", hook: "Lifestyle Integration", promise: "Demonstrate how product fits naturally" }
      ],
      scripts: [
        {
          id: "s1", angleId: "a1",
          lines: [
            "Getting ready for my day and this is essential",
            "I use this every morning without fail",
            "It's become such an important part of my routine",
            "Can't imagine starting my day without it"
          ]
        }
      ],
      captions: [
        { id: "c1", text: "Day in my life featuring my must-have! 💫 #dayinmylife #routine", platform }
      ]
    },
    review: {
      angles: [
        { id: "a1", hook: "Honest Review", promise: "Give authentic opinion and experience" },
        { id: "a2", hook: "Pros and Cons", promise: "Balanced perspective on the product" }
      ],
      scripts: [
        {
          id: "s1", angleId: "a1",
          lines: [
            "Okay, let's talk about this product honestly",
            "I've been using it for 2 weeks now",
            "Here's what I love about it...",
            "And here's my final verdict"
          ]
        }
      ],
      captions: [
        { id: "c1", text: "Honest review after 2 weeks! Worth the hype? 🤔 #review #honest", platform }
      ]
    }
  };

  // Default template
  const defaultTemplate = {
    angles: [
      { id: "a1", hook: "Problem -> Solution", promise: "Show how product solves a real problem" },
      { id: "a2", hook: "Lifestyle Moment", promise: "Natural integration into daily life" }
    ],
    scripts: [
      {
        id: "s1", angleId: "a1",
        lines: [
          "You know that feeling when...",
          "Well, I found something that actually works",
          "Let me show you what I mean",
          "This has been a total game changer for me"
        ]
      }
    ],
    captions: [
      { id: "c1", text: "This changed everything for me! ✨ #musthave #gameChanger", platform }
    ]
  };

  return contentTemplates[contentType] || defaultTemplate;
}

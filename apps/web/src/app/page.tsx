"use client";

import { useEffect, useMemo, useState } from "react";
import Link from "next/link";

type Agent = {
  id: string;
  name: string;
  description: string;
  status: "idle" | "ready" | "busy";
};

type BriefInput = {
  topic: string;
  objective: string;
  tone: string;
  platforms: string[];
};

type Brief = {
  id: string;
  content: string;
  createdAt: number;
};

type ChatMessage = {
  id: string;
  role: "user" | "agent" | "system";
  content: string;
  ts: number;
};

export default function Home() {
  const [theme, setTheme] = useState<"dark" | "light">("dark");
  const [agents] = useState<Agent[]>([
    {
      id: "briefGenerator",
      name: "Brief Generator",
      description:
        "Generates creative UGC briefs with hooks, scripts, and captions.",
      status: "ready",
    },
  ]);

  const [input, setInput] = useState<BriefInput>({
    topic: "",
    objective: "",
    tone: "confident",
    platforms: ["tiktok", "instagram"],
  });

  const [isSubmitting, setSubmitting] = useState(false);
  const [briefs, setBriefs] = useState<Brief[]>([]);
  const [events, setEvents] = useState<string[]>([]);

  // Chat state
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: crypto.randomUUID(),
      role: "system",
      content:
        "You are chatting with UGC Studio agents. Ask for ideas, hooks, scripts, or briefs.",
      ts: Date.now(),
    },
  ]);
  const [inputChat, setInputChat] = useState("");

  // Theme sync: html[data-theme]
  useEffect(() => {
    const html = document.documentElement;
    html.setAttribute("data-theme", theme);
  }, [theme]);

  // SSE stream
  useEffect(() => {
    let closed = false;
    const evts = new EventSource("/api/events/stream");
    const ts = () => new Date().toLocaleTimeString();

    const push = (label: string) =>
      setEvents((prev) => [`${label} ${ts()}`, ...prev].slice(0, 50));

    evts.addEventListener("hello", () => push("hello"));
    evts.addEventListener("heartbeat", () => push("heartbeat"));
    evts.addEventListener("message", (e) => {
      try {
        const d = JSON.parse((e as MessageEvent).data);
        push(d?.type ?? "message");
      } catch {
        push("message");
      }
    });
    evts.onerror = () => {
      if (!closed) setEvents((prev) => [`stream error`, ...prev].slice(0, 50));
    };
    return () => {
      closed = true;
      evts.close();
    };
  }, []);

  const themeLabel = useMemo(
    () => (theme === "dark" ? "Dark" : "Light"),
    [theme]
  );

  // Generate brief with normalization
  const onGenerate = async () => {
    if (!input.topic.trim() || !input.objective.trim()) {
      setEvents((prev) =>
        [`please provide topic and objective`, ...prev].slice(0, 50)
      );
      return;
    }
    setSubmitting(true);
    try {
      const res = await fetch("/api/briefs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: input.topic,
          objective: input.objective,
          tone: input.tone,
          platforms: input.platforms,
        }),
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(`HTTP ${res.status} ${text}`);
      }
      const data = await res.json();
      const payload: any[] = Array.isArray(data) ? data : [data];
      const prepared: Brief[] = payload.map((b, i) => ({
        id: b.id ?? crypto.randomUUID(),
        content:
          b.content ??
          (b as any).brief ??
          (b as any).text ??
          (typeof b === "string" ? b : JSON.stringify(b, null, 2)),
        createdAt: Date.now() + i,
      }));
      setBriefs((prev) => [...prepared, ...prev].slice(0, 8));
      setEvents((prev) =>
        [`brief generated (${prepared.length})`, ...prev].slice(0, 50)
      );
    } catch (e: any) {
      setEvents((prev) =>
        [`generation failed: ${e?.message ?? e}`, ...prev].slice(0, 50)
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Chat handler: routes prompt to brief generator when asked
  const sendChat = async () => {
    const text = inputChat.trim();
    if (!text) return;
    const userMsg: ChatMessage = {
      id: crypto.randomUUID(),
      role: "user",
      content: text,
      ts: Date.now(),
    };
    setMessages((prev) => [...prev, userMsg]);
    setInputChat("");

    const lower = text.toLowerCase();
    if (lower.includes("brief") || lower.startsWith("generate")) {
      setEvents((prev) =>
        [`chat → generating brief`, ...prev].slice(0, 50)
      );
      try {
        const res = await fetch("/api/briefs", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            title: input.topic || text.slice(0, 60),
            objective: input.objective || "General UGC ideation from chat",
            tone: input.tone,
            platforms: input.platforms,
            prompt: text,
          }),
        });
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        const payload: any[] = Array.isArray(data) ? data : [data];
        const prepared: Brief[] = payload.map((b: any, i: number) => ({
          id: b.id ?? crypto.randomUUID(),
          content:
            b.content ??
            b.brief ??
            b.text ??
            (typeof b === "string" ? b : JSON.stringify(b, null, 2)),
          createdAt: Date.now() + i,
        }));
        setBriefs((prev) => [...prepared, ...prev].slice(0, 8));
        const reply: ChatMessage = {
          id: crypto.randomUUID(),
          role: "agent",
          content: prepared.map((p) => p.content).join("\n\n"),
          ts: Date.now(),
        };
        setMessages((prev) => [...prev, reply]);
      } catch (e: any) {
        setEvents((prev) =>
          [`chat generation failed: ${e?.message ?? e}`, ...prev].slice(0, 50)
        );
        const err: ChatMessage = {
          id: crypto.randomUUID(),
          role: "agent",
          content: "I couldn't generate a brief right now.",
          ts: Date.now(),
        };
        setMessages((prev) => [...prev, err]);
      }
    } else {
      const tip: ChatMessage = {
        id: crypto.randomUUID(),
        role: "agent",
        content:
          "Tip: Ask me to generate a brief, e.g. “Generate a UGC brief for a fintech app targeting Gen Z on TikTok, playful tone.”",
        ts: Date.now(),
      };
      setMessages((prev) => [...prev, tip]);
    }
  };

  const togglePlatform = (key: string) => {
    setInput((prev) => {
      const has = prev.platforms.includes(key);
      return {
        ...prev,
        platforms: has
          ? prev.platforms.filter((p) => p !== key)
          : [...prev.platforms, key],
      };
    });
  };

  return (
    <main>
      <div className="container">
        <header className="header">
          <div className="brand">
            <span className="dot" />
            <div>
              <div style={{ fontWeight: 700, letterSpacing: "0.02em" }}>
                UGC Studio
              </div>
              <div className="badge pulse" style={{ marginTop: 4 }}>
                Agents Active
              </div>
            </div>
          </div>
          <div style={{ display: "flex", gap: 8, alignItems: "center" }}>
            <Link href="/campaigns" className="btn ghost">
              Campaigns
            </Link>
            <Link href="/briefs" className="btn ghost">
              Briefs
            </Link>
            <Link href="/avatars" className="btn ghost">
              Avatars
            </Link>
            <Link href="/pipeline" className="btn ghost">
              Pipeline
            </Link>
            <Link href="/api-docs" className="btn ghost">
              API Docs
            </Link>
            <button
              className="btn ghost"
              onClick={() => setTheme((t) => (t === "dark" ? "light" : "dark"))}
              title="Toggle theme"
            >
              {themeLabel} mode
            </button>
          </div>
        </header>

        <section className="studio" style={{ marginTop: 12, marginBottom: 24 }}>
          {/* Left: Agents */}
          <aside className="panel" style={{ padding: 12 }}>
            <div className="panel-title">
              <span>Agents</span>
              <span className="badge">1 available</span>
            </div>
            <div className="panel-body">
              <div className="list">
                {agents.map((a) => (
                  <div key={a.id} className="list-item">
                    <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
                      <span className="badge" style={{ textTransform: "capitalize" }}>
                        {a.status}
                      </span>
                      <div>
                        <div style={{ fontWeight: 600 }}>{a.name}</div>
                        <div style={{ fontSize: 12, color: "var(--muted)" }}>
                          {a.description}
                        </div>
                      </div>
                    </div>
                    <button className="btn ghost" disabled>
                      Call
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </aside>

          {/* Center: Brief Generator + Chat */}
          <section className="panel" style={{ padding: 12 }}>
            <div className="panel-title">
              <span>Brief Generator</span>
              <span className="badge">briefGenerator</span>
            </div>
            <div className="panel-body" style={{ display: "grid", gap: 12 }}>
              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: 12,
                    color: "var(--muted)",
                    marginBottom: 6,
                  }}
                >
                  Topic
                </label>
                <input
                  className="input"
                  placeholder="e.g. Launching a new eco-friendly water bottle"
                  value={input.topic}
                  onChange={(e) => setInput((p) => ({ ...p, topic: e.target.value }))}
                />
              </div>
              <div>
                <label
                  style={{
                    display: "block",
                    fontSize: 12,
                    color: "var(--muted)",
                    marginBottom: 6,
                  }}
                >
                  Objective
                </label>
                <input
                  className="input"
                  placeholder="e.g. Drive awareness and collect 200 UGC posts in 2 weeks"
                  value={input.objective}
                  onChange={(e) =>
                    setInput((p) => ({ ...p, objective: e.target.value }))
                  }
                />
              </div>
              <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 12 }}>
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: 12,
                      color: "var(--muted)",
                      marginBottom: 6,
                    }}
                  >
                    Tone
                  </label>
                  <select
                    className="select"
                    value={input.tone}
                    onChange={(e) => setInput((p) => ({ ...p, tone: e.target.value }))}
                  >
                    <option value="confident">Confident</option>
                    <option value="playful">Playful</option>
                    <option value="inspirational">Inspirational</option>
                    <option value="educational">Educational</option>
                  </select>
                </div>
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: 12,
                      color: "var(--muted)",
                      marginBottom: 6,
                    }}
                  >
                    Platforms
                  </label>
                  <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                    {["tiktok", "instagram", "youtube", "x"].map((p) => {
                      const active = input.platforms.includes(p);
                      return (
                        <button
                          key={p}
                          type="button"
                          className="badge"
                          style={{
                            borderColor: active
                              ? "var(--primary)"
                              : "var(--border)",
                            color: active ? "var(--primary)" : "var(--muted)",
                          }}
                          onClick={() => togglePlatform(p)}
                        >
                          {active ? "●" : "○"} {p}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Inline Chat Interface */}
              <div
                className="panel"
                style={{
                  padding: 10,
                  background: "transparent",
                  border: "1px dashed var(--border)",
                }}
              >
                <div
                  className="panel-title"
                  style={{ borderBottomStyle: "solid" }}
                >
                  <span>Chat</span>
                  <span className="badge">{messages.length} messages</span>
                </div>
                <div className="panel-body" style={{ display: "grid", gap: 10 }}>
                  <div
                    style={{
                      maxHeight: 220,
                      overflow: "auto",
                      display: "grid",
                      gap: 8,
                    }}
                  >
                    {messages.map((m) => (
                      <div
                        key={m.id}
                        className="list-item"
                        style={{
                          justifyContent: "flex-start",
                          gap: 12,
                          background:
                            m.role === "user"
                              ? "color-mix(in oklab, var(--primary) 8%, transparent)"
                              : "color-mix(in oklab, var(--glass) 92%, transparent)",
                          borderColor:
                            m.role === "user"
                              ? "color-mix(in oklab, var(--primary) 40%, var(--border))"
                              : "var(--border)",
                        }}
                      >
                        <span
                          className="badge"
                          style={{
                            textTransform: "capitalize",
                            minWidth: 64,
                            justifyContent: "center",
                          }}
                        >
                          {m.role}
                        </span>
                        <div style={{ whiteSpace: "pre-wrap" }}>{m.content}</div>
                      </div>
                    ))}
                  </div>
                  <div style={{ display: "flex", gap: 8 }}>
                    <input
                      className="input"
                      placeholder="Ask for hooks, scripts, or 'Generate a brief for ...'"
                      value={inputChat}
                      onChange={(e) => setInputChat(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          sendChat();
                        }
                      }}
                    />
                    <button className="btn" onClick={sendChat}>
                      Send
                    </button>
                  </div>
                </div>
              </div>

              <div
                style={{ display: "flex", justifyContent: "flex-end", gap: 8, marginTop: 6 }}
              >
                <button
                  className="btn ghost"
                  onClick={() =>
                    setInput({
                      topic: "",
                      objective: "",
                      tone: "confident",
                      platforms: ["tiktok", "instagram"],
                    })
                  }
                >
                  Reset
                </button>
                <button className="btn" onClick={onGenerate} disabled={isSubmitting}>
                  {isSubmitting ? "Generating..." : "Generate Brief"}
                </button>
              </div>
            </div>
          </section>

          {/* Right: Activity */}
          <aside className="panel" style={{ padding: 12 }}>
            <div className="panel-title">
              <span>Activity</span>
              <span className="badge">{events.length} events</span>
            </div>
            <div
              className="panel-body"
              style={{ display: "grid", gap: 8, maxHeight: 420, overflow: "auto" }}
            >
              {events.length === 0 ? (
                <div className="badge">Waiting for events…</div>
              ) : (
                events.map((e, i) => (
                  <div
                    key={i}
                    className="list-item"
                    style={{ fontSize: 12, color: "var(--muted)" }}
                  >
                    {e}
                  </div>
                ))
              )}
            </div>
          </aside>
        </section>

        {/* Results */}
        <section className="panel" style={{ padding: 12, marginBottom: 36 }}>
          <div className="panel-title">
            <span>Results</span>
            <span className="badge">{briefs.length} briefs</span>
          </div>
          <div className="panel-body" style={{ display: "grid", gap: 12 }}>
            {briefs.length === 0 ? (
              <div className="list-item" style={{ color: "var(--muted)" }}>
                No briefs yet. Fill the generator and click Generate.
              </div>
            ) : (
              briefs.map((b) => (
                <div key={b.id} className="list-item" style={{ alignItems: "flex-start" }}>
                  <div style={{ display: "grid", gap: 6, width: "100%" }}>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <div className="badge">
                        Created {new Date(b.createdAt).toLocaleTimeString()}
                      </div>
                      <button
                        className="btn ghost"
                        onClick={() => navigator.clipboard.writeText(b.content)}
                        title="Copy brief"
                      >
                        Copy
                      </button>
                    </div>
                    <pre
                      style={{
                        margin: 0,
                        whiteSpace: "pre-wrap",
                        fontFamily: "var(--font-geist-mono)",
                        fontSize: 12,
                      }}
                    >
{String(b.content)}
                    </pre>
                  </div>
                </div>
              ))
            )}
          </div>
        </section>
      </div>
    </main>
  );
}

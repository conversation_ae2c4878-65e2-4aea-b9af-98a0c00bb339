'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Brief {
  id: number;
  campaignId: number;
  title: string;
  content: string;
  generatedBy: string;
  createdAt: string;
}

export default function BriefsPage() {
  const [briefs, setBriefs] = useState<Brief[]>([]);
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isApproving, setIsApproving] = useState<number | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // SSE Connection
  useEffect(() => {
    const es = new EventSource('/api/events/stream');
    setEventSource(es);
    setConnectionStatus('connecting');

    es.onopen = () => {
      setConnectionStatus('connected');
    };

    es.onmessage = (event) => {
      console.log('SSE message:', event.data);
    };

    es.addEventListener('hello', (event) => {
      console.log('SSE hello:', event.data);
    });

    es.addEventListener('heartbeat', (event) => {
      console.log('SSE heartbeat:', event.data);
    });

    es.onerror = () => {
      setConnectionStatus('disconnected');
    };

    return () => {
      es.close();
      setEventSource(null);
      setConnectionStatus('disconnected');
    };
  }, []);

  const generateBrief = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setMessage(null);

    try {
      // Generate a title based on the prompt
      const briefTitle = generateBriefTitle(prompt);

      const response = await fetch('/api/briefs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaignId: 1, // Default to first campaign for MVP
          title: briefTitle,
          prompt: prompt // Include the user's prompt
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: `Brief "${result.title}" generated successfully!` });
        // Add the new brief to the list
        const newBrief: Brief = {
          id: result.id,
          campaignId: result.campaignId,
          title: result.title,
          content: result.content || '{}',
          generatedBy: 'agent',
          createdAt: new Date().toISOString()
        };
        setBriefs(prev => [newBrief, ...prev]);
        setPrompt(''); // Clear the prompt after successful generation
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to generate brief' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsGenerating(false);
    }
  };

  const generateBriefTitle = (prompt: string): string => {
    // Simple logic to generate a title from the prompt
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('tiktok')) return 'TikTok Content Brief';
    if (lowerPrompt.includes('instagram')) return 'Instagram Content Brief';
    if (lowerPrompt.includes('tutorial')) return 'Tutorial Content Brief';
    if (lowerPrompt.includes('review')) return 'Product Review Brief';
    if (lowerPrompt.includes('unboxing')) return 'Unboxing Content Brief';
    if (lowerPrompt.includes('day in the life') || lowerPrompt.includes('daily routine')) return 'Lifestyle Content Brief';

    return 'AI Generated Content Brief';
  };

  const approveBrief = async (briefId: number, status: 'approved' | 'rejected') => {
    setIsApproving(briefId);
    setMessage(null);

    try {
      const response = await fetch(`/api/briefs/${briefId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          note: status === 'approved' ? 'Looks good' : 'Needs revision',
          userId: 1 // Placeholder for auth
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `Brief ${status === 'approved' ? 'approved' : 'rejected'} successfully!` 
        });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update approval status' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsApproving(null);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'disconnected': return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <Link href="/campaigns" className="text-blue-600 hover:text-blue-800">
              Campaigns
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Briefs</span>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Content Briefs</h1>
              <p className="text-gray-600">
                Describe the content you want and let AI generate detailed briefs for creators.
              </p>
            </div>
            <div className={`text-sm ${getConnectionStatusColor()}`}>
              ● {connectionStatus === 'connected' ? 'Live' : connectionStatus}
            </div>
          </div>

          {/* AI Prompt Interface */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="briefPrompt" className="block text-sm font-medium text-gray-700 mb-2">
                  Describe the content you want 🎬
                </label>
                <textarea
                  id="briefPrompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Create a TikTok video showing a morning skincare routine with our new serum. The creator should be a young woman in her 20s, filming in natural lighting. Include before/after shots and emphasize the glowing skin results..."
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  💡 Specify platform, content type, target creator, and key messages
                </div>
                <button
                  onClick={generateBrief}
                  disabled={isGenerating || !prompt.trim()}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGenerating ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </span>
                  ) : 'Generate Brief ✨'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Message Display */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}

        {/* Example Prompts */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-900 mb-3">Example Content Prompts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <button
              onClick={() => setPrompt("Create a TikTok video showing a morning skincare routine with our new serum. The creator should be a young woman in her 20s, filming in natural lighting. Include before/after shots and emphasize the glowing skin results.")}
              className="text-left p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              🌅 Morning skincare routine TikTok
            </button>
            <button
              onClick={() => setPrompt("Need an Instagram Reel of someone unboxing our fitness equipment. Show the excitement of receiving the package, unpacking each item, and a quick demo of one exercise. Keep it energetic and motivational.")}
              className="text-left p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              📦 Fitness equipment unboxing Reel
            </button>
            <button
              onClick={() => setPrompt("Create a 'day in the life' video featuring our productivity app. Show a busy professional using the app throughout their day - planning morning tasks, tracking progress, and celebrating completed goals.")}
              className="text-left p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              📱 Day in the life with productivity app
            </button>
            <button
              onClick={() => setPrompt("Want a food review video of our new protein bars. Creator should try different flavors, give honest reactions, and explain how they fit into their workout routine. Make it authentic and relatable.")}
              className="text-left p-2 text-sm text-blue-700 hover:bg-blue-100 rounded border border-blue-200"
            >
              🍫 Protein bar taste test review
            </button>
          </div>
        </div>

        {/* Briefs List */}
        <div className="space-y-4">
          {briefs.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <div className="text-gray-400 text-6xl mb-4">🎬</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No briefs generated yet</h3>
              <p className="text-gray-500 mb-4">Describe the content you want above and generate your first AI-powered brief</p>
            </div>
          ) : (
            briefs.map((brief) => (
              <div key={brief.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{brief.title}</h3>
                    <p className="text-sm text-gray-500 mb-3">
                      Campaign ID: {brief.campaignId} • Generated by: {brief.generatedBy} • {new Date(brief.createdAt).toLocaleString()}
                    </p>
                    {brief.content && brief.content !== '{}' && (
                      <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-700">
                        <div className="font-medium mb-1">Generated Content:</div>
                        <div className="whitespace-pre-wrap">{typeof brief.content === 'string' ? brief.content : JSON.stringify(brief.content, null, 2)}</div>
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => approveBrief(brief.id, 'approved')}
                      disabled={isApproving === brief.id}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      {isApproving === brief.id ? 'Processing...' : '✓ Approve'}
                    </button>
                    <button
                      onClick={() => approveBrief(brief.id, 'rejected')}
                      disabled={isApproving === brief.id}
                      className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      {isApproving === brief.id ? 'Processing...' : '✗ Reject'}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Info Panel */}
        <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">How AI Brief Generation Works</h3>
          <div className="text-sm text-gray-700 space-y-1">
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">1</span>
              Describe your content vision - platform, creator type, key messages, and style
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">2</span>
              AI analyzes your prompt and generates detailed creator briefs with scripts and guidelines
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">3</span>
              Review, approve, or reject briefs to maintain quality control
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">4</span>
              Live connection provides real-time updates via Server-Sent Events
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

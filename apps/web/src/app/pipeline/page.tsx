'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { LoadingState } from '../../components/ui/LoadingSpinner';
import { ErrorState } from '../../components/ui/ErrorState';

interface PipelineStage {
  stage: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  error?: string;
  outputUrl?: string;
}

interface PipelineExecution {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  currentStage?: string;
  stages: PipelineStage[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  metadata?: Record<string, any>;
}

export default function PipelinePage() {
  const [executions, setExecutions] = useState<PipelineExecution[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<PipelineExecution | null>(null);

  const startTestPipeline = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          briefId: 1, // Use first brief for testing
          avatarId: 1, // Use first avatar
          platforms: ['tiktok', 'instagram'],
          metadata: {
            testRun: true,
            startedBy: 'user'
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start pipeline');
      }

      const result = await response.json();
      
      // Add the new execution to the list
      const newExecution: PipelineExecution = {
        id: result.executionId,
        status: 'running',
        currentStage: 'tts-generation',
        stages: result.stages.map((stage: string) => ({
          stage,
          status: stage === 'tts-generation' ? 'processing' : 'pending'
        })),
        createdAt: result.createdAt,
        updatedAt: result.createdAt,
        metadata: {
          briefId: result.briefId,
          renderScriptId: result.renderScriptId
        }
      };

      setExecutions(prev => [newExecution, ...prev]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start pipeline');
    } finally {
      setLoading(false);
    }
  };

  const refreshExecution = async (executionId: string) => {
    try {
      const response = await fetch(`/api/pipeline?executionId=${executionId}`);
      
      if (response.ok) {
        const updatedExecution = await response.json();
        setExecutions(prev => 
          prev.map(exec => 
            exec.id === executionId ? updatedExecution : exec
          )
        );
        
        if (selectedExecution?.id === executionId) {
          setSelectedExecution(updatedExecution);
        }
      }
    } catch (err) {
      console.error('Failed to refresh execution:', err);
    }
  };

  const getStageIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'processing':
        return '⏳';
      case 'failed':
        return '❌';
      default:
        return '⭕';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'running':
      case 'processing':
        return 'text-blue-600 bg-blue-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'cancelled':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-yellow-600 bg-yellow-100';
    }
  };

  const formatStageTitle = (stage: string) => {
    return stage.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Pipeline Monitor</span>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Pipeline Monitor</h1>
            <p className="text-gray-600">
              Monitor and manage AI content generation pipelines in real-time.
            </p>
          </div>
          <button
            onClick={startTestPipeline}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Starting...' : 'Start Test Pipeline'}
          </button>
        </div>

        {error && (
          <div className="mb-6">
            <ErrorState
              message={error}
              variant="inline"
            />
          </div>
        )}

        {/* Pipeline Executions */}
        <div className="space-y-6">
          {executions.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-gray-400 text-6xl mb-4">🔄</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No pipeline executions</h3>
              <p className="text-gray-600 mb-4">Start a test pipeline to see the AI content generation process in action.</p>
              <button
                onClick={startTestPipeline}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {loading ? 'Starting...' : 'Start Test Pipeline'}
              </button>
            </div>
          ) : (
            executions.map((execution) => (
              <div key={execution.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      Pipeline {execution.id}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Created: {new Date(execution.createdAt).toLocaleString()}</span>
                      <span>•</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(execution.status)}`}>
                        {execution.status.toUpperCase()}
                      </span>
                      {execution.currentStage && (
                        <>
                          <span>•</span>
                          <span>Current: {formatStageTitle(execution.currentStage)}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => refreshExecution(execution.id)}
                      className="text-gray-500 hover:text-gray-700 px-3 py-1 text-sm border border-gray-300 rounded"
                    >
                      Refresh
                    </button>
                    <button
                      onClick={() => setSelectedExecution(execution)}
                      className="bg-blue-600 text-white px-3 py-1 text-sm rounded hover:bg-blue-700"
                    >
                      View Details
                    </button>
                  </div>
                </div>

                {/* Pipeline Stages */}
                <div className="grid grid-cols-5 gap-4">
                  {execution.stages.map((stage, index) => (
                    <div key={stage.stage} className="text-center">
                      <div className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center text-lg mb-2 ${
                        stage.status === 'completed' ? 'bg-green-100' :
                        stage.status === 'processing' ? 'bg-blue-100' :
                        stage.status === 'failed' ? 'bg-red-100' : 'bg-gray-100'
                      }`}>
                        {getStageIcon(stage.status)}
                      </div>
                      <div className="text-xs font-medium text-gray-900 mb-1">
                        {formatStageTitle(stage.stage)}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded ${getStatusColor(stage.status)}`}>
                        {stage.status}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Execution Detail Modal */}
        {selectedExecution && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-start justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      Pipeline {selectedExecution.id}
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className={`px-3 py-1 rounded font-medium ${getStatusColor(selectedExecution.status)}`}>
                        {selectedExecution.status.toUpperCase()}
                      </span>
                      <span>Created: {new Date(selectedExecution.createdAt).toLocaleString()}</span>
                      <span>Updated: {new Date(selectedExecution.updatedAt).toLocaleString()}</span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedExecution(null)}
                    className="text-gray-400 hover:text-gray-600 text-2xl"
                  >
                    ✕
                  </button>
                </div>

                {/* Detailed Stage Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Pipeline Stages</h3>
                  {selectedExecution.stages.map((stage, index) => (
                    <div key={stage.stage} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{getStageIcon(stage.status)}</span>
                          <h4 className="font-medium text-gray-900">
                            {index + 1}. {formatStageTitle(stage.stage)}
                          </h4>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(stage.status)}`}>
                          {stage.status}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 space-y-1">
                        {stage.startedAt && (
                          <div>Started: {new Date(stage.startedAt).toLocaleString()}</div>
                        )}
                        {stage.completedAt && (
                          <div>Completed: {new Date(stage.completedAt).toLocaleString()}</div>
                        )}
                        {stage.error && (
                          <div className="text-red-600">Error: {stage.error}</div>
                        )}
                        {stage.outputUrl && (
                          <div>
                            Output: <a href={stage.outputUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              {stage.outputUrl}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Metadata */}
                {selectedExecution.metadata && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(selectedExecution.metadata, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => refreshExecution(selectedExecution.id)}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Refresh
                  </button>
                  <button
                    onClick={() => setSelectedExecution(null)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

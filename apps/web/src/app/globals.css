@import "tailwindcss";

/* Theme tokens */
:root {
  --radius: 12px;

  --bg: #0b0b0f;
  --bg-elev: #101016;
  --bg-soft: #0d0d13;

  --text: #e5e7eb;
  --muted: #9aa1b2;

  --primary: #6d7cff;
  --primary-700: #5968ff;
  --primary-800: #4856f7;

  --accent: #22d3ee;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;

  --border: #202433;
  --card: rgba(255,255,255,0.04);
  --glass: rgba(255,255,255,0.06);

  --ring: 0 0 0 2px color-mix(in oklab, var(--primary) 40%, transparent);
}

/* Light theme overrides (if user switches data-theme="light") */
html[data-theme="light"] {
  --bg: #f7f8fb;
  --bg-elev: #ffffff;
  --bg-soft: #f2f4f7;

  --text: #0f172a;
  --muted: #475569;

  --primary: #3b82f6;
  --primary-700: #2563eb;
  --primary-800: #1d4ed8;

  --accent: #0ea5e9;
  --success: #16a34a;
  --warning: #d97706;
  --danger: #dc2626;

  --border: #e5e7eb;
  --card: rgba(0,0,0,0.04);
  --glass: rgba(0,0,0,0.06);
}

/* Tailwind theme bridge */
@theme inline {
  --color-background: var(--bg);
  --color-foreground: var(--text);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Base styles */
* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  background: radial-gradient(1200px 600px at 80% -20%, color-mix(in oklab, var(--primary) 25%, transparent), transparent 60%),
              radial-gradient(900px 500px at -10% 20%, color-mix(in oklab, var(--accent) 18%, transparent), transparent 60%),
              var(--bg);
  color: var(--text);
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background 300ms ease, color 300ms ease;
}

/* Utility tokens */
.container {
  max-width: 1200px;
  margin-inline: auto;
  padding-inline: clamp(16px, 3vw, 28px);
}

/* Glass panel */
.panel {
  background: linear-gradient(180deg, color-mix(in oklab, var(--glass) 90%, transparent), color-mix(in oklab, var(--glass) 60%, transparent));
  border: 1px solid var(--border);
  border-radius: var(--radius);
  backdrop-filter: saturate(140%) blur(8px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.25), inset 0 1px 0 rgba(255,255,255,0.02);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-radius: calc(var(--radius) - 4px);
  padding: 10px 14px;
  font-weight: 600;
  font-size: 14px;
  line-height: 1;
  border: 1px solid color-mix(in oklab, var(--primary) 35%, var(--border));
  color: white;
  background: linear-gradient(180deg, var(--primary), var(--primary-700));
  box-shadow: 0 10px 22px color-mix(in oklab, var(--primary) 20%, transparent), inset 0 1px 0 rgba(255,255,255,0.1);
  transition: transform 120ms ease, box-shadow 200ms ease, background 200ms ease, opacity 150ms ease;
}
.btn:hover { transform: translateY(-1px); box-shadow: 0 14px 28px color-mix(in oklab, var(--primary) 28%, transparent), inset 0 1px 0 rgba(255,255,255,0.12); }
.btn:active { transform: translateY(0); }
.btn.ghost {
  background: transparent;
  color: var(--text);
  border-color: var(--border);
}
.btn.ghost:hover {
  background: color-mix(in oklab, var(--glass) 80%, transparent);
}

/* Inputs */
.input, .select, textarea {
  width: 100%;
  border-radius: calc(var(--radius) - 4px);
  background: var(--bg-soft);
  border: 1px solid var(--border);
  color: var(--text);
  padding: 10px 12px;
  outline: none;
  transition: border-color 150ms ease, box-shadow 150ms ease, background 200ms ease;
}
.input:focus, .select:focus, textarea:focus {
  border-color: color-mix(in oklab, var(--primary) 55%, var(--border));
  box-shadow: var(--ring);
}

/* Chips */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: color-mix(in oklab, var(--glass) 90%, transparent);
  border: 1px solid var(--border);
  border-radius: 999px;
  padding: 6px 10px;
  font-size: 12px;
  color: var(--muted);
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 20px 0 12px;
}
.brand {
  display: flex; align-items: center; gap: 12px;
}
.brand .dot {
  width: 10px; height: 10px; border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, white, var(--accent) 35%, transparent 60%), var(--accent);
  box-shadow: 0 0 24px color-mix(in oklab, var(--accent) 60%, transparent);
}

/* Grid */
.studio {
  display: grid;
  grid-template-columns: 320px 1fr 360px;
  gap: 16px;
}
@media (max-width: 1100px) {
  .studio { grid-template-columns: 1fr; }
}

/* Panels */
.panel-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--muted);
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  padding-bottom: 8px;
  border-bottom: 1px dashed var(--border);
}
.panel-body { padding: 14px; }

/* Cards list */
.list {
  display: grid;
  gap: 10px;
}
.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  padding: 10px 12px;
  border-radius: calc(var(--radius) - 6px);
  background: color-mix(in oklab, var(--glass) 92%, transparent);
  border: 1px solid var(--border);
}

/* Animations */
@keyframes pulseGlow {
  0% { box-shadow: 0 0 0 0 color-mix(in oklab, var(--accent) 35%, transparent); }
  70% { box-shadow: 0 0 0 12px color-mix(in oklab, var(--accent) 0%, transparent); }
  100% { box-shadow: 0 0 0 0 color-mix(in oklab, var(--accent) 0%, transparent); }
}
.badge.pulse { animation: pulseGlow 2.5s ease infinite; }
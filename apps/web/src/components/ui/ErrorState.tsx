import React from 'react';

interface ErrorStateProps {
  title?: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
  variant?: 'default' | 'minimal' | 'inline';
}

export function ErrorState({ 
  title = 'Something went wrong',
  message, 
  action,
  className = '',
  variant = 'default'
}: ErrorStateProps) {
  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <svg className="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
        <span className="text-sm">{message}</span>
      </div>
    );
  }

  if (variant === 'minimal') {
    return (
      <div className={`text-center p-4 ${className}`}>
        <div className="text-red-600 text-4xl mb-2">⚠️</div>
        <p className="text-gray-600 text-sm">{message}</p>
        {action && (
          <button
            onClick={action.onClick}
            className="mt-3 text-sm text-blue-600 hover:text-blue-800 underline"
          >
            {action.label}
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={`text-center p-8 ${className}`}>
      <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {action && (
        <button
          onClick={action.onClick}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {action.label}
        </button>
      )}
    </div>
  );
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;
      }

      return (
        <ErrorState
          title="Application Error"
          message={this.state.error?.message || 'An unexpected error occurred'}
          action={{
            label: 'Try Again',
            onClick: this.resetError
          }}
        />
      );
    }

    return this.props.children;
  }
}

interface NetworkErrorProps {
  onRetry?: () => void;
  className?: string;
}

export function NetworkError({ onRetry, className = '' }: NetworkErrorProps) {
  return (
    <ErrorState
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection and try again."
      action={onRetry ? {
        label: 'Retry',
        onClick: onRetry
      } : undefined}
      className={className}
    />
  );
}

interface NotFoundErrorProps {
  resource?: string;
  onGoBack?: () => void;
  className?: string;
}

export function NotFoundError({ resource = 'resource', onGoBack, className = '' }: NotFoundErrorProps) {
  return (
    <ErrorState
      title="Not Found"
      message={`The ${resource} you're looking for doesn't exist or has been moved.`}
      action={onGoBack ? {
        label: 'Go Back',
        onClick: onGoBack
      } : undefined}
      className={className}
    />
  );
}

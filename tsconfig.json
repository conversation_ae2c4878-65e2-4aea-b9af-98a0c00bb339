{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./apps/web/src/*"], "@db/*": ["./packages/db/src/*"], "@queue/*": ["./packages/queue/src/*"]}}, "include": ["apps/**/*.ts", "apps/**/*.tsx", "packages/**/*.ts", "packages/**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
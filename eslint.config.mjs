import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Base configuration for all files
  {
    ignores: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/out/**',
      '**/coverage/**',
      '**/.turbo/**',
      '**/packages/db/drizzle/**',
      '**/*.tsbuildinfo',
    ],
  },
  
  // TypeScript and React configuration for web app
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  
  // Additional rules for the entire project
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    rules: {
      // Code style
      'prefer-const': 'error',
      'no-var': 'error',
      'no-unused-vars': 'off', // Handled by TypeScript
      '@typescript-eslint/no-unused-vars': [
        'error',
        { 
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_'
        }
      ],
      
      // Import organization
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            'internal',
            'parent',
            'sibling',
            'index'
          ],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true
          }
        }
      ],
      
      // React specific
      'react/jsx-uses-react': 'off',
      'react/react-in-jsx-scope': 'off',
      'react-hooks/exhaustive-deps': 'warn',
      
      // General code quality
      'no-console': 'warn',
      'no-debugger': 'error',
      'eqeqeq': ['error', 'always'],
      'curly': ['error', 'all'],
    },
  },
  
  // Specific rules for API routes
  {
    files: ['**/api/**/*.{js,ts}'],
    rules: {
      'no-console': 'off', // Allow console in API routes for logging
    },
  },
  
  // Specific rules for configuration files
  {
    files: ['**/*.config.{js,mjs,ts}', '**/eslint.config.mjs'],
    rules: {
      'import/no-default-export': 'off',
    },
  },
];

export default eslintConfig;

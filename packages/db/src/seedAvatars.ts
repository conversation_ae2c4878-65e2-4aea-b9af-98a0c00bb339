import { db } from './client';
import { creators, avatars, avatarLicenses, workspaces } from './schema';
import { eq } from 'drizzle-orm';

export async function seedAvatars() {
  console.log('Seeding avatar catalog...');

  // Get the default workspace
  const [workspace] = await db
    .select()
    .from(workspaces)
    .where(eq(workspaces.slug, 'acme'))
    .limit(1);

  if (!workspace) {
    throw new Error('Default workspace not found. Run main seed first.');
  }

  // Create creators
  const creatorData = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      bio: 'Lifestyle and beauty content creator with 500K+ followers across platforms. Specializes in authentic product reviews and daily routines.',
      profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=400',
      socialLinks: JSON.stringify([
        { platform: 'tiktok', handle: '@emmalifestyle', followers: 520000 },
        { platform: 'instagram', handle: '@emma.rodriguez', followers: 340000 }
      ]),
      demographics: JSON.stringify({
        age: 24,
        gender: 'female',
        ethnicity: 'hispanic',
        location: 'Los Angeles, CA'
      }),
      languages: JSON.stringify(['english', 'spanish']),
      specialties: JSON.stringify(['beauty', 'lifestyle', 'fashion', 'wellness']),
      rateCard: JSON.stringify({
        tiktok: { post: 2500, story: 1000 },
        instagram: { post: 3000, story: 1200, reel: 3500 }
      })
    },
    {
      name: 'Marcus Chen',
      email: '<EMAIL>',
      bio: 'Tech reviewer and fitness enthusiast. Known for honest product reviews and workout content.',
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      socialLinks: JSON.stringify([
        { platform: 'youtube', handle: '@MarcusTech', subscribers: 180000 },
        { platform: 'tiktok', handle: '@marcusreviews', followers: 95000 }
      ]),
      demographics: JSON.stringify({
        age: 28,
        gender: 'male',
        ethnicity: 'asian',
        location: 'San Francisco, CA'
      }),
      languages: JSON.stringify(['english', 'mandarin']),
      specialties: JSON.stringify(['tech', 'fitness', 'productivity', 'reviews']),
      rateCard: JSON.stringify({
        youtube: { video: 5000, short: 2000 },
        tiktok: { post: 1800, story: 800 }
      })
    },
    {
      name: 'Zara Johnson',
      email: '<EMAIL>',
      bio: 'Fashion and lifestyle creator focused on sustainable living and affordable style.',
      profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
      socialLinks: JSON.stringify([
        { platform: 'instagram', handle: '@zarastyle', followers: 280000 },
        { platform: 'tiktok', handle: '@zarafashion', followers: 150000 }
      ]),
      demographics: JSON.stringify({
        age: 26,
        gender: 'female',
        ethnicity: 'black',
        location: 'Atlanta, GA'
      }),
      languages: JSON.stringify(['english']),
      specialties: JSON.stringify(['fashion', 'sustainability', 'lifestyle', 'diy']),
      rateCard: JSON.stringify({
        instagram: { post: 2800, story: 1100, reel: 3200 },
        tiktok: { post: 2200, story: 900 }
      })
    }
  ];

  const insertedCreators = await db.insert(creators).values(creatorData).returning();
  console.log(`Created ${insertedCreators.length} creators`);

  // Create avatars for each creator
  const avatarData = [
    // Emma's avatars
    {
      creatorId: insertedCreators[0].id,
      name: 'Emma - Morning Routine',
      description: 'Perfect for skincare and wellness content. Natural lighting, cozy bedroom setting.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',
      videoSampleUrl: 'https://example.com/samples/emma-morning.mp4',
      voiceSampleUrl: 'https://example.com/samples/emma-voice.mp3',
      demographics: JSON.stringify({
        age: 24,
        gender: 'female',
        ethnicity: 'hispanic',
        style: 'natural'
      }),
      personality: JSON.stringify({
        tone: 'warm',
        energy: 'calm',
        style: 'authentic',
        vibe: 'relatable'
      }),
      languages: JSON.stringify(['english', 'spanish']),
      contentTypes: JSON.stringify(['routine', 'tutorial', 'review', 'lifestyle']),
      platforms: JSON.stringify(['tiktok', 'instagram', 'youtube']),
      tags: JSON.stringify(['skincare', 'morning', 'wellness', 'authentic', 'cozy'])
    },
    {
      creatorId: insertedCreators[0].id,
      name: 'Emma - Product Review',
      description: 'Professional setup for detailed product reviews and unboxings.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400',
      videoSampleUrl: 'https://example.com/samples/emma-review.mp4',
      voiceSampleUrl: 'https://example.com/samples/emma-voice.mp3',
      demographics: JSON.stringify({
        age: 24,
        gender: 'female',
        ethnicity: 'hispanic',
        style: 'professional'
      }),
      personality: JSON.stringify({
        tone: 'confident',
        energy: 'enthusiastic',
        style: 'informative',
        vibe: 'trustworthy'
      }),
      languages: JSON.stringify(['english', 'spanish']),
      contentTypes: JSON.stringify(['review', 'unboxing', 'comparison', 'tutorial']),
      platforms: JSON.stringify(['tiktok', 'instagram', 'youtube']),
      tags: JSON.stringify(['review', 'unboxing', 'beauty', 'honest', 'detailed'])
    },
    // Marcus's avatars
    {
      creatorId: insertedCreators[1].id,
      name: 'Marcus - Tech Review',
      description: 'Clean tech setup perfect for gadget reviews and tech tutorials.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1560472355-536de3962603?w=400',
      videoSampleUrl: 'https://example.com/samples/marcus-tech.mp4',
      voiceSampleUrl: 'https://example.com/samples/marcus-voice.mp3',
      demographics: JSON.stringify({
        age: 28,
        gender: 'male',
        ethnicity: 'asian',
        style: 'professional'
      }),
      personality: JSON.stringify({
        tone: 'analytical',
        energy: 'focused',
        style: 'informative',
        vibe: 'expert'
      }),
      languages: JSON.stringify(['english', 'mandarin']),
      contentTypes: JSON.stringify(['review', 'tutorial', 'comparison', 'unboxing']),
      platforms: JSON.stringify(['youtube', 'tiktok', 'instagram']),
      tags: JSON.stringify(['tech', 'gadgets', 'review', 'honest', 'detailed'])
    },
    {
      creatorId: insertedCreators[1].id,
      name: 'Marcus - Fitness',
      description: 'Home gym setup for fitness and wellness content.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      videoSampleUrl: 'https://example.com/samples/marcus-fitness.mp4',
      voiceSampleUrl: 'https://example.com/samples/marcus-voice.mp3',
      demographics: JSON.stringify({
        age: 28,
        gender: 'male',
        ethnicity: 'asian',
        style: 'athletic'
      }),
      personality: JSON.stringify({
        tone: 'motivational',
        energy: 'high',
        style: 'encouraging',
        vibe: 'inspiring'
      }),
      languages: JSON.stringify(['english', 'mandarin']),
      contentTypes: JSON.stringify(['workout', 'tutorial', 'motivation', 'routine']),
      platforms: JSON.stringify(['tiktok', 'instagram', 'youtube']),
      tags: JSON.stringify(['fitness', 'workout', 'motivation', 'health', 'strength'])
    },
    // Zara's avatars
    {
      creatorId: insertedCreators[2].id,
      name: 'Zara - Fashion Haul',
      description: 'Stylish bedroom setup perfect for fashion content and outfit styling.',
      thumbnailUrl: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400',
      videoSampleUrl: 'https://example.com/samples/zara-fashion.mp4',
      voiceSampleUrl: 'https://example.com/samples/zara-voice.mp3',
      demographics: JSON.stringify({
        age: 26,
        gender: 'female',
        ethnicity: 'black',
        style: 'fashionable'
      }),
      personality: JSON.stringify({
        tone: 'excited',
        energy: 'vibrant',
        style: 'trendy',
        vibe: 'inspiring'
      }),
      languages: JSON.stringify(['english']),
      contentTypes: JSON.stringify(['haul', 'styling', 'outfit', 'fashion']),
      platforms: JSON.stringify(['tiktok', 'instagram', 'youtube']),
      tags: JSON.stringify(['fashion', 'style', 'affordable', 'trendy', 'outfit'])
    }
  ];

  const insertedAvatars = await db.insert(avatars).values(avatarData).returning();
  console.log(`Created ${insertedAvatars.length} avatars`);

  // Create sample licenses for the workspace
  const licenseData = insertedAvatars.slice(0, 3).map(avatar => ({
    avatarId: avatar.id,
    workspaceId: workspace.id,
    licenseType: 'standard' as const,
    usageRights: JSON.stringify({
      platforms: ['tiktok', 'instagram', 'youtube'],
      contentTypes: ['organic', 'paid'],
      duration: '1 year',
      exclusivity: false,
      modifications: true,
      commercial: true
    }),
    restrictions: JSON.stringify({
      noCompetitors: true,
      approvalRequired: false,
      creditRequired: true,
      maxDuration: 60
    }),
    pricing: JSON.stringify({
      setup: 500,
      perVideo: 250,
      monthly: 1000,
      currency: 'USD'
    }),
    startDate: new Date(),
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    maxUsages: 50
  }));

  const insertedLicenses = await db.insert(avatarLicenses).values(licenseData).returning();
  console.log(`Created ${insertedLicenses.length} avatar licenses`);

  console.log('Avatar catalog seeded successfully!');
}

// Run the seeding
seedAvatars().catch(console.error);

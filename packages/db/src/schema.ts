import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  boolean,
  uuid,
  integer,
  index,
  unique
} from 'drizzle-orm/pg-core';

export const workspaces = pgTable('workspaces', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 120 }).notNull(),
  slug: varchar('slug', { length: 120 }).notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  archived: boolean('archived').default(false).notNull(),
}, (table) => ({
  // Indexes for performance
  slugIdx: index('workspaces_slug_idx').on(table.slug),
  archivedIdx: index('workspaces_archived_idx').on(table.archived),
  createdAtIdx: index('workspaces_created_at_idx').on(table.createdAt),
}));

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 120 }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  disabled: boolean('disabled').default(false).notNull(),
}, (table) => ({
  // Indexes for performance
  emailIdx: index('users_email_idx').on(table.email),
  disabledIdx: index('users_disabled_idx').on(table.disabled),
  createdAtIdx: index('users_created_at_idx').on(table.createdAt),
}));

export const memberships = pgTable('memberships', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  role: varchar('role', { length: 32 }).notNull(), // 'owner' | 'admin' | 'member' | 'viewer'
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceUserIdx: index('memberships_workspace_user_idx').on(table.workspaceId, table.userId),
  userIdx: index('memberships_user_idx').on(table.userId),
  roleIdx: index('memberships_role_idx').on(table.role),

  // Constraints
  uniqueWorkspaceUser: unique('memberships_workspace_user_unique').on(table.workspaceId, table.userId),
}));

export const integrations = pgTable('integrations', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 64 }).notNull(), // 'openai' | 'anthropic' | 'replicate' | 'aws' | 'gcp' | 'stripe' | 'x' | 'tiktok' | 'instagram'
  configJson: text('config_json').notNull(), // encrypted JSON
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceProviderIdx: index('integrations_workspace_provider_idx').on(table.workspaceId, table.provider),
  providerIdx: index('integrations_provider_idx').on(table.provider),

  // Constraints
  uniqueWorkspaceProvider: unique('integrations_workspace_provider_unique').on(table.workspaceId, table.provider),
}));

export const campaigns = pgTable('campaigns', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 160 }).notNull(),
  objective: varchar('objective', { length: 80 }), // awareness | conversion | engagement
  status: varchar('status', { length: 32 }).default('draft').notNull(), // draft | active | paused | archived
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceIdx: index('campaigns_workspace_idx').on(table.workspaceId),
  statusIdx: index('campaigns_status_idx').on(table.status),
  objectiveIdx: index('campaigns_objective_idx').on(table.objective),
  createdAtIdx: index('campaigns_created_at_idx').on(table.createdAt),
  workspaceStatusIdx: index('campaigns_workspace_status_idx').on(table.workspaceId, table.status),
}));

export const briefs = pgTable('briefs', {
  id: serial('id').primaryKey(),
  campaignId: integer('campaign_id').notNull().references(() => campaigns.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 200 }).notNull(),
  content: text('content').notNull(), // JSON of angles/scripts/captions
  generatedBy: varchar('generated_by', { length: 64 }).default('user').notNull(), // user | agent
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  campaignIdx: index('briefs_campaign_idx').on(table.campaignId),
  generatedByIdx: index('briefs_generated_by_idx').on(table.generatedBy),
  createdAtIdx: index('briefs_created_at_idx').on(table.createdAt),
  campaignCreatedAtIdx: index('briefs_campaign_created_at_idx').on(table.campaignId, table.createdAt),
}));

export const approvals = pgTable('approvals', {
  id: serial('id').primaryKey(),
  entityType: varchar('entity_type', { length: 64 }).notNull(), // brief | render | composition
  entityId: integer('entity_id').notNull(),
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | approved | rejected
  note: text('note'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  decidedAt: timestamp('decided_at', { withTimezone: true }),
  decidedByUserId: integer('decided_by_user_id').references(() => users.id, { onDelete: 'set null' }),
}, (table) => ({
  // Indexes for performance
  entityIdx: index('approvals_entity_idx').on(table.entityType, table.entityId),
  statusIdx: index('approvals_status_idx').on(table.status),
  decidedByIdx: index('approvals_decided_by_idx').on(table.decidedByUserId),
  createdAtIdx: index('approvals_created_at_idx').on(table.createdAt),

  // Constraints
  uniqueEntity: unique('approvals_entity_unique').on(table.entityType, table.entityId),
}));

export const auditLogs = pgTable('audit_logs', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  actorType: varchar('actor_type', { length: 32 }).notNull(), // user | agent | system
  actorId: varchar('actor_id', { length: 64 }).notNull(),
  action: varchar('action', { length: 128 }).notNull(),
  targetType: varchar('target_type', { length: 64 }).notNull(),
  targetId: varchar('target_id', { length: 64 }).notNull(),
  metadata: text('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance (audit logs are heavily queried)
  workspaceIdx: index('audit_logs_workspace_idx').on(table.workspaceId),
  actorIdx: index('audit_logs_actor_idx').on(table.actorType, table.actorId),
  targetIdx: index('audit_logs_target_idx').on(table.targetType, table.targetId),
  actionIdx: index('audit_logs_action_idx').on(table.action),
  createdAtIdx: index('audit_logs_created_at_idx').on(table.createdAt),
  workspaceCreatedAtIdx: index('audit_logs_workspace_created_at_idx').on(table.workspaceId, table.createdAt),
}));
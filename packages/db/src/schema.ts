import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  boolean,
  uuid,
  integer,
  index,
  unique
} from 'drizzle-orm/pg-core';

export const workspaces = pgTable('workspaces', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 120 }).notNull(),
  slug: varchar('slug', { length: 120 }).notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  archived: boolean('archived').default(false).notNull(),
}, (table) => ({
  // Indexes for performance
  slugIdx: index('workspaces_slug_idx').on(table.slug),
  archivedIdx: index('workspaces_archived_idx').on(table.archived),
  createdAtIdx: index('workspaces_created_at_idx').on(table.createdAt),
}));

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 120 }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  disabled: boolean('disabled').default(false).notNull(),
}, (table) => ({
  // Indexes for performance
  emailIdx: index('users_email_idx').on(table.email),
  disabledIdx: index('users_disabled_idx').on(table.disabled),
  createdAtIdx: index('users_created_at_idx').on(table.createdAt),
}));

export const memberships = pgTable('memberships', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  role: varchar('role', { length: 32 }).notNull(), // 'owner' | 'admin' | 'member' | 'viewer'
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceUserIdx: index('memberships_workspace_user_idx').on(table.workspaceId, table.userId),
  userIdx: index('memberships_user_idx').on(table.userId),
  roleIdx: index('memberships_role_idx').on(table.role),

  // Constraints
  uniqueWorkspaceUser: unique('memberships_workspace_user_unique').on(table.workspaceId, table.userId),
}));

export const integrations = pgTable('integrations', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 64 }).notNull(), // 'openai' | 'anthropic' | 'replicate' | 'aws' | 'gcp' | 'stripe' | 'x' | 'tiktok' | 'instagram'
  configJson: text('config_json').notNull(), // encrypted JSON
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceProviderIdx: index('integrations_workspace_provider_idx').on(table.workspaceId, table.provider),
  providerIdx: index('integrations_provider_idx').on(table.provider),

  // Constraints
  uniqueWorkspaceProvider: unique('integrations_workspace_provider_unique').on(table.workspaceId, table.provider),
}));

export const campaigns = pgTable('campaigns', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 160 }).notNull(),
  objective: varchar('objective', { length: 80 }), // awareness | conversion | engagement
  status: varchar('status', { length: 32 }).default('draft').notNull(), // draft | active | paused | archived
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  workspaceIdx: index('campaigns_workspace_idx').on(table.workspaceId),
  statusIdx: index('campaigns_status_idx').on(table.status),
  objectiveIdx: index('campaigns_objective_idx').on(table.objective),
  createdAtIdx: index('campaigns_created_at_idx').on(table.createdAt),
  workspaceStatusIdx: index('campaigns_workspace_status_idx').on(table.workspaceId, table.status),
}));

export const briefs = pgTable('briefs', {
  id: serial('id').primaryKey(),
  campaignId: integer('campaign_id').notNull().references(() => campaigns.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 200 }).notNull(),
  content: text('content').notNull(), // JSON of angles/scripts/captions
  generatedBy: varchar('generated_by', { length: 64 }).default('user').notNull(), // user | agent
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  campaignIdx: index('briefs_campaign_idx').on(table.campaignId),
  generatedByIdx: index('briefs_generated_by_idx').on(table.generatedBy),
  createdAtIdx: index('briefs_created_at_idx').on(table.createdAt),
  campaignCreatedAtIdx: index('briefs_campaign_created_at_idx').on(table.campaignId, table.createdAt),
}));

export const approvals = pgTable('approvals', {
  id: serial('id').primaryKey(),
  entityType: varchar('entity_type', { length: 64 }).notNull(), // brief | render | composition
  entityId: integer('entity_id').notNull(),
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | approved | rejected
  note: text('note'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  decidedAt: timestamp('decided_at', { withTimezone: true }),
  decidedByUserId: integer('decided_by_user_id').references(() => users.id, { onDelete: 'set null' }),
}, (table) => ({
  // Indexes for performance
  entityIdx: index('approvals_entity_idx').on(table.entityType, table.entityId),
  statusIdx: index('approvals_status_idx').on(table.status),
  decidedByIdx: index('approvals_decided_by_idx').on(table.decidedByUserId),
  createdAtIdx: index('approvals_created_at_idx').on(table.createdAt),

  // Constraints
  uniqueEntity: unique('approvals_entity_unique').on(table.entityType, table.entityId),
}));

export const auditLogs = pgTable('audit_logs', {
  id: serial('id').primaryKey(),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  actorType: varchar('actor_type', { length: 32 }).notNull(), // user | agent | system
  actorId: varchar('actor_id', { length: 64 }).notNull(),
  action: varchar('action', { length: 128 }).notNull(),
  targetType: varchar('target_type', { length: 64 }).notNull(),
  targetId: varchar('target_id', { length: 64 }).notNull(),
  metadata: text('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance (audit logs are heavily queried)
  workspaceIdx: index('audit_logs_workspace_idx').on(table.workspaceId),
  actorIdx: index('audit_logs_actor_idx').on(table.actorType, table.actorId),
  targetIdx: index('audit_logs_target_idx').on(table.targetType, table.targetId),
  actionIdx: index('audit_logs_action_idx').on(table.action),
  createdAtIdx: index('audit_logs_created_at_idx').on(table.createdAt),
  workspaceCreatedAtIdx: index('audit_logs_workspace_created_at_idx').on(table.workspaceId, table.createdAt),
}));

// Sprint 2: Avatar Catalog Entities
export const creators = pgTable('creators', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 120 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  bio: text('bio'),
  profileImageUrl: varchar('profile_image_url', { length: 500 }),
  socialLinks: text('social_links'), // JSON array of social media links
  demographics: text('demographics'), // JSON object with age, gender, ethnicity, etc.
  languages: text('languages'), // JSON array of supported languages
  specialties: text('specialties'), // JSON array of content specialties
  rateCard: text('rate_card'), // JSON object with pricing information
  status: varchar('status', { length: 32 }).default('active').notNull(), // active | inactive | suspended
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  emailIdx: index('creators_email_idx').on(table.email),
  statusIdx: index('creators_status_idx').on(table.status),
  createdAtIdx: index('creators_created_at_idx').on(table.createdAt),
}));

export const avatars = pgTable('avatars', {
  id: serial('id').primaryKey(),
  creatorId: integer('creator_id').notNull().references(() => creators.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 120 }).notNull(),
  description: text('description'),
  thumbnailUrl: varchar('thumbnail_url', { length: 500 }).notNull(),
  videoSampleUrl: varchar('video_sample_url', { length: 500 }),
  voiceSampleUrl: varchar('voice_sample_url', { length: 500 }),
  demographics: text('demographics'), // JSON object with age, gender, ethnicity, etc.
  personality: text('personality'), // JSON object with tone, style, energy level
  languages: text('languages'), // JSON array of supported languages
  contentTypes: text('content_types'), // JSON array of suitable content types
  platforms: text('platforms'), // JSON array of optimized platforms
  tags: text('tags'), // JSON array of searchable tags
  status: varchar('status', { length: 32 }).default('active').notNull(), // active | inactive | archived
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  creatorIdx: index('avatars_creator_idx').on(table.creatorId),
  statusIdx: index('avatars_status_idx').on(table.status),
  createdAtIdx: index('avatars_created_at_idx').on(table.createdAt),
  creatorStatusIdx: index('avatars_creator_status_idx').on(table.creatorId, table.status),
}));

export const avatarLicenses = pgTable('avatar_licenses', {
  id: serial('id').primaryKey(),
  avatarId: integer('avatar_id').notNull().references(() => avatars.id, { onDelete: 'cascade' }),
  workspaceId: integer('workspace_id').notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  licenseType: varchar('license_type', { length: 64 }).notNull(), // standard | premium | exclusive | custom
  usageRights: text('usage_rights').notNull(), // JSON object defining usage permissions
  restrictions: text('restrictions'), // JSON object defining usage restrictions
  pricing: text('pricing').notNull(), // JSON object with pricing details
  startDate: timestamp('start_date', { withTimezone: true }).notNull(),
  endDate: timestamp('end_date', { withTimezone: true }),
  maxUsages: integer('max_usages'), // null for unlimited
  currentUsages: integer('current_usages').default(0).notNull(),
  status: varchar('status', { length: 32 }).default('active').notNull(), // active | expired | suspended | cancelled
  contractUrl: varchar('contract_url', { length: 500 }), // Link to signed contract
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  avatarIdx: index('avatar_licenses_avatar_idx').on(table.avatarId),
  workspaceIdx: index('avatar_licenses_workspace_idx').on(table.workspaceId),
  statusIdx: index('avatar_licenses_status_idx').on(table.status),
  licenseTypeIdx: index('avatar_licenses_license_type_idx').on(table.licenseType),
  dateRangeIdx: index('avatar_licenses_date_range_idx').on(table.startDate, table.endDate),
  workspaceAvatarIdx: index('avatar_licenses_workspace_avatar_idx').on(table.workspaceId, table.avatarId),

  // Constraints
  uniqueWorkspaceAvatar: unique('avatar_licenses_workspace_avatar_unique').on(table.workspaceId, table.avatarId),
}));

// Sprint 2: AI Pipeline Entities
export const renderScripts = pgTable('render_scripts', {
  id: serial('id').primaryKey(),
  briefId: integer('brief_id').notNull().references(() => briefs.id, { onDelete: 'cascade' }),
  avatarId: integer('avatar_id').notNull().references(() => avatars.id, { onDelete: 'restrict' }),
  title: varchar('title', { length: 200 }).notNull(),
  script: text('script').notNull(), // The actual script content
  sceneDirections: text('scene_directions'), // JSON array of scene directions
  duration: integer('duration'), // Estimated duration in seconds
  platform: varchar('platform', { length: 32 }).notNull(), // tiktok | instagram | youtube
  format: varchar('format', { length: 32 }).notNull(), // vertical | square | horizontal
  status: varchar('status', { length: 32 }).default('draft').notNull(), // draft | approved | rejected | processing | completed
  metadata: text('metadata'), // JSON object with additional metadata
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  briefIdx: index('render_scripts_brief_idx').on(table.briefId),
  avatarIdx: index('render_scripts_avatar_idx').on(table.avatarId),
  statusIdx: index('render_scripts_status_idx').on(table.status),
  platformIdx: index('render_scripts_platform_idx').on(table.platform),
  createdAtIdx: index('render_scripts_created_at_idx').on(table.createdAt),
  briefStatusIdx: index('render_scripts_brief_status_idx').on(table.briefId, table.status),
}));

export const ttsOutputs = pgTable('tts_outputs', {
  id: serial('id').primaryKey(),
  renderScriptId: integer('render_script_id').notNull().references(() => renderScripts.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 64 }).notNull(), // elevenlabs | openai | aws | azure
  voiceId: varchar('voice_id', { length: 128 }).notNull(),
  text: text('text').notNull(),
  ssml: text('ssml'), // SSML markup if used
  audioUrl: varchar('audio_url', { length: 500 }),
  duration: integer('duration'), // Duration in milliseconds
  fileSize: integer('file_size'), // File size in bytes
  format: varchar('format', { length: 16 }).default('mp3').notNull(), // mp3 | wav | ogg
  sampleRate: integer('sample_rate').default(44100), // Sample rate in Hz
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | processing | completed | failed
  errorMessage: text('error_message'),
  metadata: text('metadata'), // JSON object with provider-specific metadata
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  renderScriptIdx: index('tts_outputs_render_script_idx').on(table.renderScriptId),
  providerIdx: index('tts_outputs_provider_idx').on(table.provider),
  statusIdx: index('tts_outputs_status_idx').on(table.status),
  voiceIdx: index('tts_outputs_voice_idx').on(table.voiceId),
  createdAtIdx: index('tts_outputs_created_at_idx').on(table.createdAt),
}));

export const lipSyncJobs = pgTable('lip_sync_jobs', {
  id: serial('id').primaryKey(),
  ttsOutputId: integer('tts_output_id').notNull().references(() => ttsOutputs.id, { onDelete: 'cascade' }),
  avatarId: integer('avatar_id').notNull().references(() => avatars.id, { onDelete: 'restrict' }),
  provider: varchar('provider', { length: 64 }).notNull(), // wav2lip | lipsync_ai | custom
  audioUrl: varchar('audio_url', { length: 500 }).notNull(),
  videoUrl: varchar('video_url', { length: 500 }),
  outputUrl: varchar('output_url', { length: 500 }),
  duration: integer('duration'), // Duration in milliseconds
  fileSize: integer('file_size'), // File size in bytes
  format: varchar('format', { length: 16 }).default('mp4').notNull(), // mp4 | webm | mov
  resolution: varchar('resolution', { length: 16 }).default('1080p'), // 720p | 1080p | 4k
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | processing | completed | failed
  progress: integer('progress').default(0), // Progress percentage 0-100
  errorMessage: text('error_message'),
  metadata: text('metadata'), // JSON object with provider-specific metadata
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  ttsOutputIdx: index('lip_sync_jobs_tts_output_idx').on(table.ttsOutputId),
  avatarIdx: index('lip_sync_jobs_avatar_idx').on(table.avatarId),
  providerIdx: index('lip_sync_jobs_provider_idx').on(table.provider),
  statusIdx: index('lip_sync_jobs_status_idx').on(table.status),
  createdAtIdx: index('lip_sync_jobs_created_at_idx').on(table.createdAt),
}));

export const compositionJobs = pgTable('composition_jobs', {
  id: serial('id').primaryKey(),
  lipSyncJobId: integer('lip_sync_job_id').notNull().references(() => lipSyncJobs.id, { onDelete: 'cascade' }),
  renderScriptId: integer('render_script_id').notNull().references(() => renderScripts.id, { onDelete: 'cascade' }),
  template: varchar('template', { length: 128 }).notNull(), // Template identifier
  backgroundUrl: varchar('background_url', { length: 500 }),
  overlayUrls: text('overlay_urls'), // JSON array of overlay asset URLs
  musicUrl: varchar('music_url', { length: 500 }),
  outputUrl: varchar('output_url', { length: 500 }),
  duration: integer('duration'), // Duration in milliseconds
  fileSize: integer('file_size'), // File size in bytes
  format: varchar('format', { length: 16 }).default('mp4').notNull(), // mp4 | webm | mov
  resolution: varchar('resolution', { length: 16 }).default('1080p'), // 720p | 1080p | 4k
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | processing | completed | failed
  progress: integer('progress').default(0), // Progress percentage 0-100
  errorMessage: text('error_message'),
  renderSettings: text('render_settings'), // JSON object with ffmpeg settings
  metadata: text('metadata'), // JSON object with additional metadata
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => ({
  // Indexes for performance
  lipSyncJobIdx: index('composition_jobs_lip_sync_job_idx').on(table.lipSyncJobId),
  renderScriptIdx: index('composition_jobs_render_script_idx').on(table.renderScriptId),
  statusIdx: index('composition_jobs_status_idx').on(table.status),
  templateIdx: index('composition_jobs_template_idx').on(table.template),
  createdAtIdx: index('composition_jobs_created_at_idx').on(table.createdAt),
}));
{"id": "59367cfc-02e7-4634-86b6-282e180d30bb", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.approvals": {"name": "approvals", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "decided_at": {"name": "decided_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "decided_by_user_id": {"name": "decided_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"approvals_entity_idx": {"name": "approvals_entity_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "approvals_status_idx": {"name": "approvals_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "approvals_decided_by_idx": {"name": "approvals_decided_by_idx", "columns": [{"expression": "decided_by_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "approvals_created_at_idx": {"name": "approvals_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"approvals_decided_by_user_id_users_id_fk": {"name": "approvals_decided_by_user_id_users_id_fk", "tableFrom": "approvals", "tableTo": "users", "columnsFrom": ["decided_by_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"approvals_entity_unique": {"name": "approvals_entity_unique", "nullsNotDistinct": false, "columns": ["entity_type", "entity_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "actor_type": {"name": "actor_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "actor_id": {"name": "actor_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "target_type": {"name": "target_type", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"audit_logs_workspace_idx": {"name": "audit_logs_workspace_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_actor_idx": {"name": "audit_logs_actor_idx", "columns": [{"expression": "actor_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "actor_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_target_idx": {"name": "audit_logs_target_idx", "columns": [{"expression": "target_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "target_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_action_idx": {"name": "audit_logs_action_idx", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_created_at_idx": {"name": "audit_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_workspace_created_at_idx": {"name": "audit_logs_workspace_created_at_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"audit_logs_workspace_id_workspaces_id_fk": {"name": "audit_logs_workspace_id_workspaces_id_fk", "tableFrom": "audit_logs", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.briefs": {"name": "briefs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "campaign_id": {"name": "campaign_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "generated_by": {"name": "generated_by", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"briefs_campaign_idx": {"name": "briefs_campaign_idx", "columns": [{"expression": "campaign_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "briefs_generated_by_idx": {"name": "briefs_generated_by_idx", "columns": [{"expression": "generated_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "briefs_created_at_idx": {"name": "briefs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "briefs_campaign_created_at_idx": {"name": "briefs_campaign_created_at_idx", "columns": [{"expression": "campaign_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"briefs_campaign_id_campaigns_id_fk": {"name": "briefs_campaign_id_campaigns_id_fk", "tableFrom": "briefs", "tableTo": "campaigns", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campaigns": {"name": "campaigns", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(160)", "primaryKey": false, "notNull": true}, "objective": {"name": "objective", "type": "<PERSON><PERSON><PERSON>(80)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"campaigns_workspace_idx": {"name": "campaigns_workspace_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "campaigns_status_idx": {"name": "campaigns_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "campaigns_objective_idx": {"name": "campaigns_objective_idx", "columns": [{"expression": "objective", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "campaigns_created_at_idx": {"name": "campaigns_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "campaigns_workspace_status_idx": {"name": "campaigns_workspace_status_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"campaigns_workspace_id_workspaces_id_fk": {"name": "campaigns_workspace_id_workspaces_id_fk", "tableFrom": "campaigns", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "config_json": {"name": "config_json", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"integrations_workspace_provider_idx": {"name": "integrations_workspace_provider_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "integrations_provider_idx": {"name": "integrations_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"integrations_workspace_id_workspaces_id_fk": {"name": "integrations_workspace_id_workspaces_id_fk", "tableFrom": "integrations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"integrations_workspace_provider_unique": {"name": "integrations_workspace_provider_unique", "nullsNotDistinct": false, "columns": ["workspace_id", "provider"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.memberships": {"name": "memberships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"memberships_workspace_user_idx": {"name": "memberships_workspace_user_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "memberships_user_idx": {"name": "memberships_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "memberships_role_idx": {"name": "memberships_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"memberships_workspace_id_workspaces_id_fk": {"name": "memberships_workspace_id_workspaces_id_fk", "tableFrom": "memberships", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "memberships_user_id_users_id_fk": {"name": "memberships_user_id_users_id_fk", "tableFrom": "memberships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"memberships_workspace_user_unique": {"name": "memberships_workspace_user_unique", "nullsNotDistinct": false, "columns": ["workspace_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_disabled_idx": {"name": "users_disabled_idx", "columns": [{"expression": "disabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"workspaces_slug_idx": {"name": "workspaces_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workspaces_archived_idx": {"name": "workspaces_archived_idx", "columns": [{"expression": "archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workspaces_created_at_idx": {"name": "workspaces_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_slug_unique": {"name": "workspaces_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
-- Sprint 2: Avatar Catalog & Pipeline Entities

-- Creators table
CREATE TABLE "creators" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(120) NOT NULL,
	"email" varchar(255) NOT NULL,
	"bio" text,
	"profile_image_url" varchar(500),
	"social_links" text,
	"demographics" text,
	"languages" text,
	"specialties" text,
	"rate_card" text,
	"status" varchar(32) DEFAULT 'active' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "creators_email_unique" UNIQUE("email")
);

-- Avatars table
CREATE TABLE "avatars" (
	"id" serial PRIMARY KEY NOT NULL,
	"creator_id" integer NOT NULL,
	"name" varchar(120) NOT NULL,
	"description" text,
	"thumbnail_url" varchar(500) NOT NULL,
	"video_sample_url" varchar(500),
	"voice_sample_url" varchar(500),
	"demographics" text,
	"personality" text,
	"languages" text,
	"content_types" text,
	"platforms" text,
	"tags" text,
	"status" varchar(32) DEFAULT 'active' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- Avatar Licenses table
CREATE TABLE "avatar_licenses" (
	"id" serial PRIMARY KEY NOT NULL,
	"avatar_id" integer NOT NULL,
	"workspace_id" integer NOT NULL,
	"license_type" varchar(64) NOT NULL,
	"usage_rights" text NOT NULL,
	"restrictions" text,
	"pricing" text NOT NULL,
	"start_date" timestamp with time zone NOT NULL,
	"end_date" timestamp with time zone,
	"max_usages" integer,
	"current_usages" integer DEFAULT 0 NOT NULL,
	"status" varchar(32) DEFAULT 'active' NOT NULL,
	"contract_url" varchar(500),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "avatar_licenses_workspace_avatar_unique" UNIQUE("workspace_id","avatar_id")
);

-- Render Scripts table
CREATE TABLE "render_scripts" (
	"id" serial PRIMARY KEY NOT NULL,
	"brief_id" integer NOT NULL,
	"avatar_id" integer NOT NULL,
	"title" varchar(200) NOT NULL,
	"script" text NOT NULL,
	"scene_directions" text,
	"duration" integer,
	"platform" varchar(32) NOT NULL,
	"format" varchar(32) NOT NULL,
	"status" varchar(32) DEFAULT 'draft' NOT NULL,
	"metadata" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- TTS Outputs table
CREATE TABLE "tts_outputs" (
	"id" serial PRIMARY KEY NOT NULL,
	"render_script_id" integer NOT NULL,
	"provider" varchar(64) NOT NULL,
	"voice_id" varchar(128) NOT NULL,
	"text" text NOT NULL,
	"ssml" text,
	"audio_url" varchar(500),
	"duration" integer,
	"file_size" integer,
	"format" varchar(16) DEFAULT 'mp3' NOT NULL,
	"sample_rate" integer DEFAULT 44100,
	"status" varchar(32) DEFAULT 'pending' NOT NULL,
	"error_message" text,
	"metadata" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- Lip Sync Jobs table
CREATE TABLE "lip_sync_jobs" (
	"id" serial PRIMARY KEY NOT NULL,
	"tts_output_id" integer NOT NULL,
	"avatar_id" integer NOT NULL,
	"provider" varchar(64) NOT NULL,
	"audio_url" varchar(500) NOT NULL,
	"video_url" varchar(500),
	"output_url" varchar(500),
	"duration" integer,
	"file_size" integer,
	"format" varchar(16) DEFAULT 'mp4' NOT NULL,
	"resolution" varchar(16) DEFAULT '1080p',
	"status" varchar(32) DEFAULT 'pending' NOT NULL,
	"progress" integer DEFAULT 0,
	"error_message" text,
	"metadata" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- Composition Jobs table
CREATE TABLE "composition_jobs" (
	"id" serial PRIMARY KEY NOT NULL,
	"lip_sync_job_id" integer NOT NULL,
	"render_script_id" integer NOT NULL,
	"template" varchar(128) NOT NULL,
	"background_url" varchar(500),
	"overlay_urls" text,
	"music_url" varchar(500),
	"output_url" varchar(500),
	"duration" integer,
	"file_size" integer,
	"format" varchar(16) DEFAULT 'mp4' NOT NULL,
	"resolution" varchar(16) DEFAULT '1080p',
	"status" varchar(32) DEFAULT 'pending' NOT NULL,
	"progress" integer DEFAULT 0,
	"error_message" text,
	"render_settings" text,
	"metadata" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- Foreign Key Constraints
ALTER TABLE "avatars" ADD CONSTRAINT "avatars_creator_id_creators_id_fk" FOREIGN KEY ("creator_id") REFERENCES "creators"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "avatar_licenses" ADD CONSTRAINT "avatar_licenses_avatar_id_avatars_id_fk" FOREIGN KEY ("avatar_id") REFERENCES "avatars"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "avatar_licenses" ADD CONSTRAINT "avatar_licenses_workspace_id_workspaces_id_fk" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "render_scripts" ADD CONSTRAINT "render_scripts_brief_id_briefs_id_fk" FOREIGN KEY ("brief_id") REFERENCES "briefs"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "render_scripts" ADD CONSTRAINT "render_scripts_avatar_id_avatars_id_fk" FOREIGN KEY ("avatar_id") REFERENCES "avatars"("id") ON DELETE restrict ON UPDATE no action;
ALTER TABLE "tts_outputs" ADD CONSTRAINT "tts_outputs_render_script_id_render_scripts_id_fk" FOREIGN KEY ("render_script_id") REFERENCES "render_scripts"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "lip_sync_jobs" ADD CONSTRAINT "lip_sync_jobs_tts_output_id_tts_outputs_id_fk" FOREIGN KEY ("tts_output_id") REFERENCES "tts_outputs"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "lip_sync_jobs" ADD CONSTRAINT "lip_sync_jobs_avatar_id_avatars_id_fk" FOREIGN KEY ("avatar_id") REFERENCES "avatars"("id") ON DELETE restrict ON UPDATE no action;
ALTER TABLE "composition_jobs" ADD CONSTRAINT "composition_jobs_lip_sync_job_id_lip_sync_jobs_id_fk" FOREIGN KEY ("lip_sync_job_id") REFERENCES "lip_sync_jobs"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "composition_jobs" ADD CONSTRAINT "composition_jobs_render_script_id_render_scripts_id_fk" FOREIGN KEY ("render_script_id") REFERENCES "render_scripts"("id") ON DELETE cascade ON UPDATE no action;

-- Indexes for performance
CREATE INDEX "creators_email_idx" ON "creators" USING btree ("email");
CREATE INDEX "creators_status_idx" ON "creators" USING btree ("status");
CREATE INDEX "creators_created_at_idx" ON "creators" USING btree ("created_at");

CREATE INDEX "avatars_creator_idx" ON "avatars" USING btree ("creator_id");
CREATE INDEX "avatars_status_idx" ON "avatars" USING btree ("status");
CREATE INDEX "avatars_created_at_idx" ON "avatars" USING btree ("created_at");
CREATE INDEX "avatars_creator_status_idx" ON "avatars" USING btree ("creator_id","status");

CREATE INDEX "avatar_licenses_avatar_idx" ON "avatar_licenses" USING btree ("avatar_id");
CREATE INDEX "avatar_licenses_workspace_idx" ON "avatar_licenses" USING btree ("workspace_id");
CREATE INDEX "avatar_licenses_status_idx" ON "avatar_licenses" USING btree ("status");
CREATE INDEX "avatar_licenses_license_type_idx" ON "avatar_licenses" USING btree ("license_type");
CREATE INDEX "avatar_licenses_date_range_idx" ON "avatar_licenses" USING btree ("start_date","end_date");
CREATE INDEX "avatar_licenses_workspace_avatar_idx" ON "avatar_licenses" USING btree ("workspace_id","avatar_id");

CREATE INDEX "render_scripts_brief_idx" ON "render_scripts" USING btree ("brief_id");
CREATE INDEX "render_scripts_avatar_idx" ON "render_scripts" USING btree ("avatar_id");
CREATE INDEX "render_scripts_status_idx" ON "render_scripts" USING btree ("status");
CREATE INDEX "render_scripts_platform_idx" ON "render_scripts" USING btree ("platform");
CREATE INDEX "render_scripts_created_at_idx" ON "render_scripts" USING btree ("created_at");
CREATE INDEX "render_scripts_brief_status_idx" ON "render_scripts" USING btree ("brief_id","status");

CREATE INDEX "tts_outputs_render_script_idx" ON "tts_outputs" USING btree ("render_script_id");
CREATE INDEX "tts_outputs_provider_idx" ON "tts_outputs" USING btree ("provider");
CREATE INDEX "tts_outputs_status_idx" ON "tts_outputs" USING btree ("status");
CREATE INDEX "tts_outputs_voice_idx" ON "tts_outputs" USING btree ("voice_id");
CREATE INDEX "tts_outputs_created_at_idx" ON "tts_outputs" USING btree ("created_at");

CREATE INDEX "lip_sync_jobs_tts_output_idx" ON "lip_sync_jobs" USING btree ("tts_output_id");
CREATE INDEX "lip_sync_jobs_avatar_idx" ON "lip_sync_jobs" USING btree ("avatar_id");
CREATE INDEX "lip_sync_jobs_provider_idx" ON "lip_sync_jobs" USING btree ("provider");
CREATE INDEX "lip_sync_jobs_status_idx" ON "lip_sync_jobs" USING btree ("status");
CREATE INDEX "lip_sync_jobs_created_at_idx" ON "lip_sync_jobs" USING btree ("created_at");

CREATE INDEX "composition_jobs_lip_sync_job_idx" ON "composition_jobs" USING btree ("lip_sync_job_id");
CREATE INDEX "composition_jobs_render_script_idx" ON "composition_jobs" USING btree ("render_script_id");
CREATE INDEX "composition_jobs_status_idx" ON "composition_jobs" USING btree ("status");
CREATE INDEX "composition_jobs_template_idx" ON "composition_jobs" USING btree ("template");
CREATE INDEX "composition_jobs_created_at_idx" ON "composition_jobs" USING btree ("created_at");

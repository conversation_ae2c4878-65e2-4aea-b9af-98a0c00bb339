import { z } from 'zod';

// Job Types
export const JobType = z.enum([
  'tts-generation',
  'lip-sync',
  'composition',
  'upload',
  'notification'
]);

export type JobType = z.infer<typeof JobType>;

// Job Status
export const JobStatus = z.enum([
  'pending',
  'processing',
  'completed',
  'failed',
  'retrying',
  'cancelled'
]);

export type JobStatus = z.infer<typeof JobStatus>;

// Base Job Data Schema
export const BaseJobData = z.object({
  id: z.string(),
  type: JobType,
  workspaceId: z.number(),
  campaignId: z.number().optional(),
  briefId: z.number().optional(),
  renderScriptId: z.number().optional(),
  correlationId: z.string(),
  priority: z.number().default(0),
  retryCount: z.number().default(0),
  maxRetries: z.number().default(3),
  createdAt: z.date().default(() => new Date()),
  metadata: z.record(z.any()).optional()
});

export type BaseJobData = z.infer<typeof BaseJobData>;

// TTS Job Data
export const TTSJobData = BaseJobData.extend({
  type: z.literal('tts-generation'),
  renderScriptId: z.number(),
  provider: z.enum(['elevenlabs', 'openai', 'aws', 'azure']),
  voiceId: z.string(),
  text: z.string(),
  ssml: z.string().optional(),
  settings: z.object({
    speed: z.number().optional(),
    pitch: z.number().optional(),
    volume: z.number().optional(),
    format: z.enum(['mp3', 'wav', 'ogg']).default('mp3'),
    sampleRate: z.number().default(44100)
  }).optional()
});

export type TTSJobData = z.infer<typeof TTSJobData>;

// Lip Sync Job Data
export const LipSyncJobData = BaseJobData.extend({
  type: z.literal('lip-sync'),
  ttsOutputId: z.number(),
  avatarId: z.number(),
  provider: z.enum(['wav2lip', 'lipsync_ai', 'custom']),
  audioUrl: z.string(),
  videoUrl: z.string().optional(),
  settings: z.object({
    resolution: z.enum(['720p', '1080p', '4k']).default('1080p'),
    format: z.enum(['mp4', 'webm', 'mov']).default('mp4'),
    quality: z.enum(['low', 'medium', 'high']).default('medium')
  }).optional()
});

export type LipSyncJobData = z.infer<typeof LipSyncJobData>;

// Composition Job Data
export const CompositionJobData = BaseJobData.extend({
  type: z.literal('composition'),
  lipSyncJobId: z.number(),
  renderScriptId: z.number(),
  template: z.string(),
  assets: z.object({
    backgroundUrl: z.string().optional(),
    overlayUrls: z.array(z.string()).optional(),
    musicUrl: z.string().optional(),
    logoUrl: z.string().optional()
  }).optional(),
  settings: z.object({
    resolution: z.enum(['720p', '1080p', '4k']).default('1080p'),
    format: z.enum(['mp4', 'webm', 'mov']).default('mp4'),
    quality: z.enum(['low', 'medium', 'high']).default('high'),
    duration: z.number().optional()
  }).optional()
});

export type CompositionJobData = z.infer<typeof CompositionJobData>;

// Upload Job Data
export const UploadJobData = BaseJobData.extend({
  type: z.literal('upload'),
  compositionJobId: z.number(),
  platforms: z.array(z.enum(['tiktok', 'instagram', 'youtube', 'twitter'])),
  videoUrl: z.string(),
  caption: z.string(),
  hashtags: z.array(z.string()).optional(),
  scheduledAt: z.date().optional()
});

export type UploadJobData = z.infer<typeof UploadJobData>;

// Notification Job Data
export const NotificationJobData = BaseJobData.extend({
  type: z.literal('notification'),
  userId: z.number().optional(),
  channel: z.enum(['email', 'webhook', 'slack', 'discord']),
  template: z.string(),
  data: z.record(z.any())
});

export type NotificationJobData = z.infer<typeof NotificationJobData>;

// Union of all job data types
export const JobData = z.discriminatedUnion('type', [
  TTSJobData,
  LipSyncJobData,
  CompositionJobData,
  UploadJobData,
  NotificationJobData
]);

export type JobData = z.infer<typeof JobData>;

// Job Result
export const JobResult = z.object({
  success: z.boolean(),
  data: z.record(z.any()).optional(),
  error: z.string().optional(),
  outputUrl: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  duration: z.number().optional(), // Processing time in ms
  completedAt: z.date().default(() => new Date())
});

export type JobResult = z.infer<typeof JobResult>;

// Queue Configuration
export const QueueConfig = z.object({
  name: z.string(),
  redis: z.object({
    host: z.string().default('localhost'),
    port: z.number().default(6379),
    password: z.string().optional(),
    db: z.number().default(0)
  }),
  defaultJobOptions: z.object({
    removeOnComplete: z.number().default(100),
    removeOnFail: z.number().default(50),
    attempts: z.number().default(3),
    backoff: z.object({
      type: z.enum(['fixed', 'exponential']).default('exponential'),
      delay: z.number().default(2000)
    })
  }).optional()
});

export type QueueConfig = z.infer<typeof QueueConfig>;

// Event Types
export const EventType = z.enum([
  'job.created',
  'job.started',
  'job.progress',
  'job.completed',
  'job.failed',
  'job.retrying',
  'pipeline.started',
  'pipeline.completed',
  'pipeline.failed'
]);

export type EventType = z.infer<typeof EventType>;

// Event Data
export const EventData = z.object({
  type: EventType,
  jobId: z.string(),
  correlationId: z.string(),
  workspaceId: z.number(),
  timestamp: z.date().default(() => new Date()),
  data: z.record(z.any()).optional(),
  error: z.string().optional()
});

export type EventData = z.infer<typeof EventData>;

// Pipeline Stage
export const PipelineStage = z.enum([
  'script-generation',
  'tts-generation',
  'lip-sync',
  'composition',
  'upload',
  'notification'
]);

export type PipelineStage = z.infer<typeof PipelineStage>;

// Pipeline Status
export const PipelineStatus = z.enum([
  'pending',
  'running',
  'completed',
  'failed',
  'cancelled'
]);

export type PipelineStatus = z.infer<typeof PipelineStatus>;

// Pipeline Execution
export const PipelineExecution = z.object({
  id: z.string(),
  workspaceId: z.number(),
  briefId: z.number(),
  renderScriptId: z.number(),
  status: PipelineStatus,
  currentStage: PipelineStage.optional(),
  stages: z.array(z.object({
    stage: PipelineStage,
    status: JobStatus,
    jobId: z.string().optional(),
    startedAt: z.date().optional(),
    completedAt: z.date().optional(),
    error: z.string().optional(),
    outputUrl: z.string().optional()
  })),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  completedAt: z.date().optional(),
  metadata: z.record(z.any()).optional()
});

export type PipelineExecution = z.infer<typeof PipelineExecution>;

import { TTSJobData, JobResult } from '../types.js';

export interface TTSProvider {
  name: string;
  generateSpeech(jobData: TTSJobData): Promise<TTSResult>;
  getVoices(): Promise<Voice[]>;
  validateVoice(voiceId: string): Promise<boolean>;
}

export interface TTSResult {
  audioUrl: string;
  duration: number; // in milliseconds
  fileSize: number; // in bytes
  format: string;
  sampleRate: number;
  metadata?: Record<string, any>;
}

export interface Voice {
  id: string;
  name: string;
  language: string;
  gender: 'male' | 'female' | 'neutral';
  age: 'young' | 'middle' | 'old';
  accent?: string;
  description?: string;
  previewUrl?: string;
}

// ElevenLabs Provider (Stub)
export class ElevenLabsProvider implements TTSProvider {
  name = 'elevenlabs';

  constructor(private apiKey: string) {}

  async generateSpeech(jobData: TTSJobData): Promise<TTSResult> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // In real implementation, this would call ElevenLabs API
    console.log(`[ElevenLabs] Generating speech for job ${jobData.id}`);
    console.log(`Voice: ${jobData.voiceId}, Text length: ${jobData.text.length}`);

    // Simulate successful generation
    return {
      audioUrl: `https://storage.example.com/tts/${jobData.id}.mp3`,
      duration: jobData.text.length * 50, // Rough estimate: 50ms per character
      fileSize: jobData.text.length * 1000, // Rough estimate: 1KB per character
      format: jobData.settings?.format || 'mp3',
      sampleRate: jobData.settings?.sampleRate || 44100,
      metadata: {
        provider: 'elevenlabs',
        voiceId: jobData.voiceId,
        model: 'eleven_monolingual_v1'
      }
    };
  }

  async getVoices(): Promise<Voice[]> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));

    return [
      {
        id: 'rachel',
        name: 'Rachel',
        language: 'en-US',
        gender: 'female',
        age: 'young',
        description: 'Calm, confident American female voice'
      },
      {
        id: 'josh',
        name: 'Josh',
        language: 'en-US',
        gender: 'male',
        age: 'middle',
        description: 'Professional American male voice'
      },
      {
        id: 'bella',
        name: 'Bella',
        language: 'en-US',
        gender: 'female',
        age: 'young',
        description: 'Energetic, friendly female voice'
      }
    ];
  }

  async validateVoice(voiceId: string): Promise<boolean> {
    const voices = await this.getVoices();
    return voices.some(voice => voice.id === voiceId);
  }
}

// OpenAI Provider (Stub)
export class OpenAIProvider implements TTSProvider {
  name = 'openai';

  constructor(private apiKey: string) {}

  async generateSpeech(jobData: TTSJobData): Promise<TTSResult> {
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log(`[OpenAI] Generating speech for job ${jobData.id}`);

    return {
      audioUrl: `https://storage.example.com/tts/${jobData.id}.mp3`,
      duration: jobData.text.length * 45,
      fileSize: jobData.text.length * 800,
      format: jobData.settings?.format || 'mp3',
      sampleRate: jobData.settings?.sampleRate || 44100,
      metadata: {
        provider: 'openai',
        voiceId: jobData.voiceId,
        model: 'tts-1'
      }
    };
  }

  async getVoices(): Promise<Voice[]> {
    return [
      {
        id: 'alloy',
        name: 'Alloy',
        language: 'en-US',
        gender: 'neutral',
        age: 'middle',
        description: 'Balanced, versatile voice'
      },
      {
        id: 'echo',
        name: 'Echo',
        language: 'en-US',
        gender: 'male',
        age: 'middle',
        description: 'Clear, professional male voice'
      },
      {
        id: 'nova',
        name: 'Nova',
        language: 'en-US',
        gender: 'female',
        age: 'young',
        description: 'Bright, energetic female voice'
      }
    ];
  }

  async validateVoice(voiceId: string): Promise<boolean> {
    const voices = await this.getVoices();
    return voices.some(voice => voice.id === voiceId);
  }
}

// TTS Adapter - Factory for different providers
export class TTSAdapter {
  private providers: Map<string, TTSProvider> = new Map();

  registerProvider(provider: TTSProvider): void {
    this.providers.set(provider.name, provider);
  }

  getProvider(name: string): TTSProvider | undefined {
    return this.providers.get(name);
  }

  async processJob(jobData: TTSJobData): Promise<JobResult> {
    try {
      const provider = this.getProvider(jobData.provider);
      if (!provider) {
        throw new Error(`TTS provider '${jobData.provider}' not found`);
      }

      // Validate voice
      const isValidVoice = await provider.validateVoice(jobData.voiceId);
      if (!isValidVoice) {
        throw new Error(`Voice '${jobData.voiceId}' not available for provider '${jobData.provider}'`);
      }

      const startTime = Date.now();
      const result = await provider.generateSpeech(jobData);
      const duration = Date.now() - startTime;

      return {
        success: true,
        data: result,
        outputUrl: result.audioUrl,
        duration,
        metadata: {
          provider: jobData.provider,
          voiceId: jobData.voiceId,
          textLength: jobData.text.length,
          audioFormat: result.format,
          audioDuration: result.duration,
          fileSize: result.fileSize
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown TTS error',
        duration: 0
      };
    }
  }

  async getAllVoices(): Promise<Record<string, Voice[]>> {
    const allVoices: Record<string, Voice[]> = {};
    
    for (const [providerName, provider] of this.providers) {
      try {
        allVoices[providerName] = await provider.getVoices();
      } catch (error) {
        console.error(`Failed to get voices from ${providerName}:`, error);
        allVoices[providerName] = [];
      }
    }
    
    return allVoices;
  }
}

// Default TTS adapter instance
export const ttsAdapter = new TTSAdapter();

// Register default providers (would be configured with real API keys in production)
if (process.env.ELEVENLABS_API_KEY) {
  ttsAdapter.registerProvider(new ElevenLabsProvider(process.env.ELEVENLABS_API_KEY));
}

if (process.env.OPENAI_API_KEY) {
  ttsAdapter.registerProvider(new OpenAIProvider(process.env.OPENAI_API_KEY));
}

// For development, register stub providers
if (!process.env.ELEVENLABS_API_KEY && !process.env.OPENAI_API_KEY) {
  ttsAdapter.registerProvider(new ElevenLabsProvider('dev-key'));
  ttsAdapter.registerProvider(new OpenAIProvider('dev-key'));
}

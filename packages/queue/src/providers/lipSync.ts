import { LipSyncJobData, JobResult } from '../types.js';

export interface LipSyncProvider {
  name: string;
  generateLipSync(jobData: LipSyncJobData): Promise<LipSyncResult>;
  getSupportedFormats(): string[];
  getMaxDuration(): number; // in seconds
}

export interface LipSyncResult {
  videoUrl: string;
  duration: number; // in milliseconds
  fileSize: number; // in bytes
  format: string;
  resolution: string;
  metadata?: Record<string, any>;
}

// Wav2Lip Provider (Stub)
export class Wav2LipProvider implements LipSyncProvider {
  name = 'wav2lip';

  async generateLipSync(jobData: LipSyncJobData): Promise<LipSyncResult> {
    // Simulate processing time based on video duration
    const processingTime = Math.random() * 5000 + 3000; // 3-8 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));

    console.log(`[Wav2Lip] Processing lip sync for job ${jobData.id}`);
    console.log(`Audio: ${jobData.audioUrl}, Avatar: ${jobData.avatarId}`);

    // Simulate successful processing
    return {
      videoUrl: `https://storage.example.com/lipsync/${jobData.id}.mp4`,
      duration: 30000, // 30 seconds
      fileSize: 15 * 1024 * 1024, // 15MB
      format: jobData.settings?.format || 'mp4',
      resolution: jobData.settings?.resolution || '1080p',
      metadata: {
        provider: 'wav2lip',
        avatarId: jobData.avatarId,
        audioUrl: jobData.audioUrl,
        processingTime: processingTime,
        model: 'wav2lip_gan'
      }
    };
  }

  getSupportedFormats(): string[] {
    return ['mp4', 'webm', 'mov'];
  }

  getMaxDuration(): number {
    return 300; // 5 minutes
  }
}

// LipSync AI Provider (Stub)
export class LipSyncAIProvider implements LipSyncProvider {
  name = 'lipsync_ai';

  async generateLipSync(jobData: LipSyncJobData): Promise<LipSyncResult> {
    const processingTime = Math.random() * 3000 + 2000; // 2-5 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));

    console.log(`[LipSyncAI] Processing lip sync for job ${jobData.id}`);

    return {
      videoUrl: `https://storage.example.com/lipsync/${jobData.id}.mp4`,
      duration: 30000,
      fileSize: 12 * 1024 * 1024, // 12MB (better compression)
      format: jobData.settings?.format || 'mp4',
      resolution: jobData.settings?.resolution || '1080p',
      metadata: {
        provider: 'lipsync_ai',
        avatarId: jobData.avatarId,
        audioUrl: jobData.audioUrl,
        processingTime: processingTime,
        model: 'lipsync_ai_v2'
      }
    };
  }

  getSupportedFormats(): string[] {
    return ['mp4', 'webm'];
  }

  getMaxDuration(): number {
    return 600; // 10 minutes
  }
}

// Custom Provider (Stub)
export class CustomLipSyncProvider implements LipSyncProvider {
  name = 'custom';

  constructor(private config: {
    apiUrl: string;
    apiKey: string;
    model?: string;
  }) {}

  async generateLipSync(jobData: LipSyncJobData): Promise<LipSyncResult> {
    const processingTime = Math.random() * 4000 + 2500; // 2.5-6.5 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));

    console.log(`[Custom] Processing lip sync for job ${jobData.id}`);
    console.log(`API URL: ${this.config.apiUrl}`);

    return {
      videoUrl: `https://storage.example.com/lipsync/${jobData.id}.mp4`,
      duration: 30000,
      fileSize: 18 * 1024 * 1024, // 18MB (high quality)
      format: jobData.settings?.format || 'mp4',
      resolution: jobData.settings?.resolution || '1080p',
      metadata: {
        provider: 'custom',
        avatarId: jobData.avatarId,
        audioUrl: jobData.audioUrl,
        processingTime: processingTime,
        model: this.config.model || 'custom_v1',
        apiUrl: this.config.apiUrl
      }
    };
  }

  getSupportedFormats(): string[] {
    return ['mp4', 'webm', 'mov', 'avi'];
  }

  getMaxDuration(): number {
    return 1200; // 20 minutes
  }
}

// LipSync Adapter - Factory for different providers
export class LipSyncAdapter {
  private providers: Map<string, LipSyncProvider> = new Map();

  registerProvider(provider: LipSyncProvider): void {
    this.providers.set(provider.name, provider);
  }

  getProvider(name: string): LipSyncProvider | undefined {
    return this.providers.get(name);
  }

  async processJob(jobData: LipSyncJobData): Promise<JobResult> {
    try {
      const provider = this.getProvider(jobData.provider);
      if (!provider) {
        throw new Error(`LipSync provider '${jobData.provider}' not found`);
      }

      // Validate format
      const supportedFormats = provider.getSupportedFormats();
      const requestedFormat = jobData.settings?.format || 'mp4';
      if (!supportedFormats.includes(requestedFormat)) {
        throw new Error(`Format '${requestedFormat}' not supported by provider '${jobData.provider}'`);
      }

      // Check duration limits (would need to get audio duration from TTS result)
      const maxDuration = provider.getMaxDuration();
      // For now, we'll skip this check since we don't have the actual audio duration

      const startTime = Date.now();
      const result = await provider.generateLipSync(jobData);
      const duration = Date.now() - startTime;

      return {
        success: true,
        data: result,
        outputUrl: result.videoUrl,
        duration,
        metadata: {
          provider: jobData.provider,
          avatarId: jobData.avatarId,
          audioUrl: jobData.audioUrl,
          videoFormat: result.format,
          videoDuration: result.duration,
          fileSize: result.fileSize,
          resolution: result.resolution
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown LipSync error',
        duration: 0
      };
    }
  }

  getProviderCapabilities(): Record<string, {
    formats: string[];
    maxDuration: number;
  }> {
    const capabilities: Record<string, { formats: string[]; maxDuration: number }> = {};
    
    for (const [providerName, provider] of this.providers) {
      capabilities[providerName] = {
        formats: provider.getSupportedFormats(),
        maxDuration: provider.getMaxDuration()
      };
    }
    
    return capabilities;
  }

  async validateJobData(jobData: LipSyncJobData): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Check if provider exists
    const provider = this.getProvider(jobData.provider);
    if (!provider) {
      errors.push(`Provider '${jobData.provider}' not found`);
      return { valid: false, errors };
    }

    // Check format support
    const supportedFormats = provider.getSupportedFormats();
    const requestedFormat = jobData.settings?.format || 'mp4';
    if (!supportedFormats.includes(requestedFormat)) {
      errors.push(`Format '${requestedFormat}' not supported by provider '${jobData.provider}'`);
    }

    // Check audio URL
    if (!jobData.audioUrl || !jobData.audioUrl.startsWith('http')) {
      errors.push('Valid audio URL is required');
    }

    // Check avatar ID
    if (!jobData.avatarId || jobData.avatarId <= 0) {
      errors.push('Valid avatar ID is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Default LipSync adapter instance
export const lipSyncAdapter = new LipSyncAdapter();

// Register default providers
lipSyncAdapter.registerProvider(new Wav2LipProvider());
lipSyncAdapter.registerProvider(new LipSyncAIProvider());

// Register custom provider if configured
if (process.env.CUSTOM_LIPSYNC_API_URL && process.env.CUSTOM_LIPSYNC_API_KEY) {
  lipSyncAdapter.registerProvider(new CustomLipSyncProvider({
    apiUrl: process.env.CUSTOM_LIPSYNC_API_URL,
    apiKey: process.env.CUSTOM_LIPSYNC_API_KEY,
    model: process.env.CUSTOM_LIPSYNC_MODEL
  }));
}

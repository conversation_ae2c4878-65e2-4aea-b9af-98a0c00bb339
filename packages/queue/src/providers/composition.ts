import { CompositionJobData, JobResult } from '../types.js';

export interface CompositionProvider {
  name: string;
  composeVideo(jobData: CompositionJobData): Promise<CompositionResult>;
  getAvailableTemplates(): Promise<Template[]>;
  validateTemplate(templateId: string): Promise<boolean>;
}

export interface CompositionResult {
  videoUrl: string;
  duration: number; // in milliseconds
  fileSize: number; // in bytes
  format: string;
  resolution: string;
  metadata?: Record<string, any>;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  platforms: string[];
  aspectRatio: string;
  duration: number; // max duration in seconds
  assets: {
    backgroundRequired: boolean;
    overlaysSupported: boolean;
    musicSupported: boolean;
    logoSupported: boolean;
  };
  previewUrl?: string;
}

// FFmpeg-based Composition Provider (Stub)
export class FFmpegCompositionProvider implements CompositionProvider {
  name = 'ffmpeg';

  async composeVideo(jobData: CompositionJobData): Promise<CompositionResult> {
    // Simulate video composition processing time
    const processingTime = Math.random() * 10000 + 5000; // 5-15 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));

    console.log(`[FFmpeg] Composing video for job ${jobData.id}`);
    console.log(`Template: ${jobData.template}, LipSync Job: ${jobData.lipSyncJobId}`);

    // Simulate successful composition
    return {
      videoUrl: `https://storage.example.com/composition/${jobData.id}.mp4`,
      duration: 30000, // 30 seconds
      fileSize: 25 * 1024 * 1024, // 25MB
      format: jobData.settings?.format || 'mp4',
      resolution: jobData.settings?.resolution || '1080p',
      metadata: {
        provider: 'ffmpeg',
        template: jobData.template,
        lipSyncJobId: jobData.lipSyncJobId,
        renderScriptId: jobData.renderScriptId,
        processingTime: processingTime,
        assets: jobData.assets,
        renderSettings: jobData.renderSettings
      }
    };
  }

  async getAvailableTemplates(): Promise<Template[]> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));

    return [
      {
        id: 'tiktok-vertical',
        name: 'TikTok Vertical',
        description: 'Vertical video template optimized for TikTok',
        category: 'social',
        platforms: ['tiktok', 'instagram'],
        aspectRatio: '9:16',
        duration: 60,
        assets: {
          backgroundRequired: false,
          overlaysSupported: true,
          musicSupported: true,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/tiktok-vertical.mp4'
      },
      {
        id: 'instagram-square',
        name: 'Instagram Square',
        description: 'Square video template for Instagram posts',
        category: 'social',
        platforms: ['instagram', 'twitter'],
        aspectRatio: '1:1',
        duration: 60,
        assets: {
          backgroundRequired: false,
          overlaysSupported: true,
          musicSupported: true,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/instagram-square.mp4'
      },
      {
        id: 'youtube-horizontal',
        name: 'YouTube Horizontal',
        description: 'Horizontal video template for YouTube',
        category: 'long-form',
        platforms: ['youtube'],
        aspectRatio: '16:9',
        duration: 300,
        assets: {
          backgroundRequired: true,
          overlaysSupported: true,
          musicSupported: true,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/youtube-horizontal.mp4'
      },
      {
        id: 'product-showcase',
        name: 'Product Showcase',
        description: 'Template focused on product presentation',
        category: 'commercial',
        platforms: ['tiktok', 'instagram', 'youtube'],
        aspectRatio: '9:16',
        duration: 30,
        assets: {
          backgroundRequired: false,
          overlaysSupported: true,
          musicSupported: false,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/product-showcase.mp4'
      },
      {
        id: 'testimonial',
        name: 'Testimonial',
        description: 'Clean template for testimonial content',
        category: 'testimonial',
        platforms: ['tiktok', 'instagram', 'youtube'],
        aspectRatio: '9:16',
        duration: 45,
        assets: {
          backgroundRequired: false,
          overlaysSupported: false,
          musicSupported: false,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/testimonial.mp4'
      }
    ];
  }

  async validateTemplate(templateId: string): Promise<boolean> {
    const templates = await this.getAvailableTemplates();
    return templates.some(template => template.id === templateId);
  }
}

// Cloud-based Composition Provider (Stub)
export class CloudCompositionProvider implements CompositionProvider {
  name = 'cloud';

  constructor(private config: {
    apiUrl: string;
    apiKey: string;
    region?: string;
  }) {}

  async composeVideo(jobData: CompositionJobData): Promise<CompositionResult> {
    const processingTime = Math.random() * 8000 + 3000; // 3-11 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));

    console.log(`[Cloud] Composing video for job ${jobData.id}`);
    console.log(`API URL: ${this.config.apiUrl}, Region: ${this.config.region || 'us-east-1'}`);

    return {
      videoUrl: `https://storage.example.com/composition/${jobData.id}.mp4`,
      duration: 30000,
      fileSize: 20 * 1024 * 1024, // 20MB (better compression)
      format: jobData.settings?.format || 'mp4',
      resolution: jobData.settings?.resolution || '1080p',
      metadata: {
        provider: 'cloud',
        template: jobData.template,
        lipSyncJobId: jobData.lipSyncJobId,
        renderScriptId: jobData.renderScriptId,
        processingTime: processingTime,
        region: this.config.region || 'us-east-1',
        apiUrl: this.config.apiUrl
      }
    };
  }

  async getAvailableTemplates(): Promise<Template[]> {
    // Cloud provider might have different templates
    const baseTemplates = await new FFmpegCompositionProvider().getAvailableTemplates();
    
    // Add cloud-specific templates
    return [
      ...baseTemplates,
      {
        id: 'cloud-premium',
        name: 'Cloud Premium',
        description: 'High-quality cloud-rendered template',
        category: 'premium',
        platforms: ['tiktok', 'instagram', 'youtube'],
        aspectRatio: '9:16',
        duration: 120,
        assets: {
          backgroundRequired: false,
          overlaysSupported: true,
          musicSupported: true,
          logoSupported: true
        },
        previewUrl: 'https://example.com/previews/cloud-premium.mp4'
      }
    ];
  }

  async validateTemplate(templateId: string): Promise<boolean> {
    const templates = await this.getAvailableTemplates();
    return templates.some(template => template.id === templateId);
  }
}

// Composition Adapter - Factory for different providers
export class CompositionAdapter {
  private providers: Map<string, CompositionProvider> = new Map();

  registerProvider(provider: CompositionProvider): void {
    this.providers.set(provider.name, provider);
  }

  getProvider(name: string): CompositionProvider | undefined {
    return this.providers.get(name);
  }

  async processJob(jobData: CompositionJobData): Promise<JobResult> {
    try {
      // For now, use FFmpeg provider as default
      const provider = this.getProvider('ffmpeg');
      if (!provider) {
        throw new Error('No composition provider available');
      }

      // Validate template
      const isValidTemplate = await provider.validateTemplate(jobData.template);
      if (!isValidTemplate) {
        throw new Error(`Template '${jobData.template}' not found`);
      }

      const startTime = Date.now();
      const result = await provider.composeVideo(jobData);
      const duration = Date.now() - startTime;

      return {
        success: true,
        data: result,
        outputUrl: result.videoUrl,
        duration,
        metadata: {
          provider: provider.name,
          template: jobData.template,
          lipSyncJobId: jobData.lipSyncJobId,
          renderScriptId: jobData.renderScriptId,
          videoFormat: result.format,
          videoDuration: result.duration,
          fileSize: result.fileSize,
          resolution: result.resolution,
          assets: jobData.assets
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown composition error',
        duration: 0
      };
    }
  }

  async getAllTemplates(): Promise<Record<string, Template[]>> {
    const allTemplates: Record<string, Template[]> = {};
    
    for (const [providerName, provider] of this.providers) {
      try {
        allTemplates[providerName] = await provider.getAvailableTemplates();
      } catch (error) {
        console.error(`Failed to get templates from ${providerName}:`, error);
        allTemplates[providerName] = [];
      }
    }
    
    return allTemplates;
  }

  async validateJobData(jobData: CompositionJobData): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Check template
    if (!jobData.template) {
      errors.push('Template is required');
    }

    // Check lip sync job ID
    if (!jobData.lipSyncJobId || jobData.lipSyncJobId <= 0) {
      errors.push('Valid lip sync job ID is required');
    }

    // Check render script ID
    if (!jobData.renderScriptId || jobData.renderScriptId <= 0) {
      errors.push('Valid render script ID is required');
    }

    // Validate settings
    if (jobData.settings) {
      const validFormats = ['mp4', 'webm', 'mov'];
      if (jobData.settings.format && !validFormats.includes(jobData.settings.format)) {
        errors.push(`Invalid format: ${jobData.settings.format}`);
      }

      const validResolutions = ['720p', '1080p', '4k'];
      if (jobData.settings.resolution && !validResolutions.includes(jobData.settings.resolution)) {
        errors.push(`Invalid resolution: ${jobData.settings.resolution}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Default Composition adapter instance
export const compositionAdapter = new CompositionAdapter();

// Register default providers
compositionAdapter.registerProvider(new FFmpegCompositionProvider());

// Register cloud provider if configured
if (process.env.CLOUD_COMPOSITION_API_URL && process.env.CLOUD_COMPOSITION_API_KEY) {
  compositionAdapter.registerProvider(new CloudCompositionProvider({
    apiUrl: process.env.CLOUD_COMPOSITION_API_URL,
    apiKey: process.env.CLOUD_COMPOSITION_API_KEY,
    region: process.env.CLOUD_COMPOSITION_REGION
  }));
}

import { QueueClient } from './client.js';
import { 
  PipelineExecution, 
  PipelineStatus, 
  PipelineStage,
  JobStatus,
  TTSJobData,
  LipSyncJobData,
  CompositionJobData,
  UploadJobData,
  NotificationJobData
} from './types.js';
import { randomUUID } from 'crypto';

export interface PipelineConfig {
  stages: PipelineStage[];
  queueNames: Record<PipelineStage, string>;
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
  };
}

export class PipelineOrchestrator {
  private executions: Map<string, PipelineExecution> = new Map();

  constructor(
    private queueClient: QueueClient,
    private config: PipelineConfig
  ) {
    // Set up event handlers for pipeline coordination
    this.setupEventHandlers();
  }

  async startPipeline(
    workspaceId: number,
    briefId: number,
    renderScriptId: number,
    options?: {
      avatarId?: number;
      platforms?: string[];
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    const executionId = randomUUID();
    const correlationId = randomUUID();

    const execution: PipelineExecution = {
      id: executionId,
      workspaceId,
      briefId,
      renderScriptId,
      status: 'pending',
      stages: this.config.stages.map(stage => ({
        stage,
        status: 'pending'
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: options?.metadata
    };

    this.executions.set(executionId, execution);

    // Emit pipeline started event
    await this.queueClient.onEvent('pipeline.started', async (event) => {
      console.log(`Pipeline ${executionId} started for workspace ${workspaceId}`);
    });

    // Start the first stage
    await this.processNextStage(executionId, correlationId);

    return executionId;
  }

  private async processNextStage(executionId: string, correlationId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) {
      throw new Error(`Pipeline execution ${executionId} not found`);
    }

    // Find the next pending stage
    const nextStageIndex = execution.stages.findIndex(s => s.status === 'pending');
    
    if (nextStageIndex === -1) {
      // All stages completed
      await this.completePipeline(executionId);
      return;
    }

    const stage = execution.stages[nextStageIndex];
    execution.currentStage = stage.stage;
    execution.status = 'running';
    execution.updatedAt = new Date();

    // Update stage status
    stage.status = 'processing';
    stage.startedAt = new Date();

    try {
      const jobId = await this.createStageJob(execution, stage.stage, correlationId);
      stage.jobId = jobId;
      
      console.log(`Started stage ${stage.stage} with job ${jobId} for pipeline ${executionId}`);
    } catch (error) {
      console.error(`Failed to start stage ${stage.stage} for pipeline ${executionId}:`, error);
      stage.status = 'failed';
      stage.error = error instanceof Error ? error.message : 'Unknown error';
      execution.status = 'failed';
      execution.updatedAt = new Date();
    }
  }

  private async createStageJob(
    execution: PipelineExecution,
    stage: PipelineStage,
    correlationId: string
  ): Promise<string> {
    const jobId = randomUUID();
    const queueName = this.config.queueNames[stage];

    switch (stage) {
      case 'tts-generation':
        const ttsJob: TTSJobData = {
          id: jobId,
          type: 'tts-generation',
          workspaceId: execution.workspaceId,
          briefId: execution.briefId,
          renderScriptId: execution.renderScriptId!,
          correlationId,
          provider: 'elevenlabs', // Default provider
          voiceId: 'default-voice',
          text: 'Sample text from render script', // Would be fetched from DB
          priority: 0,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date()
        };
        
        await this.queueClient.addJob(queueName, ttsJob);
        break;

      case 'lip-sync':
        const lipSyncJob: LipSyncJobData = {
          id: jobId,
          type: 'lip-sync',
          workspaceId: execution.workspaceId,
          briefId: execution.briefId,
          renderScriptId: execution.renderScriptId,
          correlationId,
          ttsOutputId: 1, // Would be from previous stage
          avatarId: execution.metadata?.avatarId || 1,
          provider: 'wav2lip',
          audioUrl: 'https://example.com/audio.mp3', // From TTS stage
          priority: 0,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date()
        };
        
        await this.queueClient.addJob(queueName, lipSyncJob);
        break;

      case 'composition':
        const compositionJob: CompositionJobData = {
          id: jobId,
          type: 'composition',
          workspaceId: execution.workspaceId,
          briefId: execution.briefId,
          renderScriptId: execution.renderScriptId!,
          correlationId,
          lipSyncJobId: 1, // Would be from previous stage
          template: 'default-template',
          priority: 0,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date()
        };
        
        await this.queueClient.addJob(queueName, compositionJob);
        break;

      case 'upload':
        const uploadJob: UploadJobData = {
          id: jobId,
          type: 'upload',
          workspaceId: execution.workspaceId,
          briefId: execution.briefId,
          correlationId,
          compositionJobId: 1, // Would be from previous stage
          platforms: execution.metadata?.platforms || ['tiktok'],
          videoUrl: 'https://example.com/video.mp4', // From composition stage
          caption: 'Generated UGC content',
          priority: 0,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date()
        };
        
        await this.queueClient.addJob(queueName, uploadJob);
        break;

      case 'notification':
        const notificationJob: NotificationJobData = {
          id: jobId,
          type: 'notification',
          workspaceId: execution.workspaceId,
          briefId: execution.briefId,
          correlationId,
          channel: 'email',
          template: 'pipeline-completed',
          data: {
            executionId: execution.id,
            briefId: execution.briefId,
            status: 'completed'
          },
          priority: 0,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date()
        };
        
        await this.queueClient.addJob(queueName, notificationJob);
        break;

      default:
        throw new Error(`Unknown pipeline stage: ${stage}`);
    }

    return jobId;
  }

  private setupEventHandlers(): void {
    // Handle job completion to advance pipeline
    this.queueClient.onEvent('job.completed', async (event) => {
      const execution = this.findExecutionByCorrelationId(event.correlationId);
      if (!execution) return;

      const stage = execution.stages.find(s => s.jobId === event.jobId);
      if (!stage) return;

      // Mark stage as completed
      stage.status = 'completed';
      stage.completedAt = new Date();
      stage.outputUrl = event.data?.result?.outputUrl;

      execution.updatedAt = new Date();

      // Process next stage
      await this.processNextStage(execution.id, event.correlationId);
    });

    // Handle job failures
    this.queueClient.onEvent('job.failed', async (event) => {
      const execution = this.findExecutionByCorrelationId(event.correlationId);
      if (!execution) return;

      const stage = execution.stages.find(s => s.jobId === event.jobId);
      if (!stage) return;

      // Mark stage as failed
      stage.status = 'failed';
      stage.error = event.error;
      stage.completedAt = new Date();

      // Mark entire pipeline as failed
      execution.status = 'failed';
      execution.updatedAt = new Date();

      console.error(`Pipeline ${execution.id} failed at stage ${stage.stage}: ${event.error}`);
    });
  }

  private findExecutionByCorrelationId(correlationId: string): PipelineExecution | undefined {
    for (const execution of this.executions.values()) {
      if (execution.stages.some(s => s.jobId && s.jobId.includes(correlationId))) {
        return execution;
      }
    }
    return undefined;
  }

  private async completePipeline(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) return;

    execution.status = 'completed';
    execution.completedAt = new Date();
    execution.updatedAt = new Date();

    console.log(`Pipeline ${executionId} completed successfully`);

    // Emit pipeline completed event
    await this.queueClient.onEvent('pipeline.completed', async (event) => {
      console.log(`Pipeline ${executionId} completed for workspace ${execution.workspaceId}`);
    });
  }

  async getPipelineStatus(executionId: string): Promise<PipelineExecution | null> {
    return this.executions.get(executionId) || null;
  }

  async cancelPipeline(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) return;

    execution.status = 'cancelled';
    execution.updatedAt = new Date();

    // Cancel any running jobs
    for (const stage of execution.stages) {
      if (stage.status === 'processing' && stage.jobId) {
        const queueName = this.config.queueNames[stage.stage];
        await this.queueClient.cancelJob(queueName, stage.jobId);
      }
    }

    console.log(`Pipeline ${executionId} cancelled`);
  }

  async retryPipeline(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) return;

    // Reset failed stages to pending
    for (const stage of execution.stages) {
      if (stage.status === 'failed') {
        stage.status = 'pending';
        stage.error = undefined;
        stage.jobId = undefined;
        stage.startedAt = undefined;
        stage.completedAt = undefined;
      }
    }

    execution.status = 'pending';
    execution.updatedAt = new Date();

    // Restart from the first failed stage
    const correlationId = randomUUID();
    await this.processNextStage(executionId, correlationId);
  }

  getActiveExecutions(): PipelineExecution[] {
    return Array.from(this.executions.values()).filter(
      e => e.status === 'running' || e.status === 'pending'
    );
  }

  getCompletedExecutions(): PipelineExecution[] {
    return Array.from(this.executions.values()).filter(
      e => e.status === 'completed' || e.status === 'failed' || e.status === 'cancelled'
    );
  }
}

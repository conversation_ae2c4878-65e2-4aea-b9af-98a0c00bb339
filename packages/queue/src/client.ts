import { Queue, Worker, Job, QueueEvents } from 'bullmq';
import Redis from 'ioredis';
import { 
  JobData, 
  JobResult, 
  QueueConfig, 
  EventData, 
  EventType,
  PipelineExecution,
  PipelineStatus,
  JobStatus
} from './types.js';

export class QueueClient {
  private redis: Redis;
  private queues: Map<string, Queue> = new Map();
  private workers: Map<string, Worker> = new Map();
  private queueEvents: Map<string, QueueEvents> = new Map();
  private eventHandlers: Map<EventType, ((event: EventData) => void)[]> = new Map();

  constructor(private config: QueueConfig) {
    this.redis = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      lazyConnect: true
    });
  }

  async connect(): Promise<void> {
    await this.redis.connect();
  }

  async disconnect(): Promise<void> {
    // Close all workers
    for (const worker of this.workers.values()) {
      await worker.close();
    }

    // Close all queue events
    for (const queueEvent of this.queueEvents.values()) {
      await queueEvent.close();
    }

    // Close all queues
    for (const queue of this.queues.values()) {
      await queue.close();
    }

    await this.redis.disconnect();
  }

  getQueue(name: string): Queue {
    if (!this.queues.has(name)) {
      const queue = new Queue(name, {
        connection: this.redis,
        defaultJobOptions: this.config.defaultJobOptions
      });
      this.queues.set(name, queue);
    }
    return this.queues.get(name)!;
  }

  async addJob(queueName: string, jobData: JobData): Promise<Job> {
    const queue = this.getQueue(queueName);
    
    const job = await queue.add(
      jobData.type,
      jobData,
      {
        jobId: jobData.id,
        priority: jobData.priority,
        attempts: jobData.maxRetries + 1,
        backoff: {
          type: 'exponential',
          delay: 2000
        },
        removeOnComplete: 100,
        removeOnFail: 50
      }
    );

    // Emit job created event
    await this.emitEvent({
      type: 'job.created',
      jobId: jobData.id,
      correlationId: jobData.correlationId,
      workspaceId: jobData.workspaceId,
      data: { queueName, jobType: jobData.type }
    });

    return job;
  }

  createWorker(
    queueName: string, 
    processor: (job: Job<JobData>) => Promise<JobResult>
  ): Worker {
    if (this.workers.has(queueName)) {
      throw new Error(`Worker for queue ${queueName} already exists`);
    }

    const worker = new Worker(
      queueName,
      async (job: Job<JobData>) => {
        try {
          // Emit job started event
          await this.emitEvent({
            type: 'job.started',
            jobId: job.data.id,
            correlationId: job.data.correlationId,
            workspaceId: job.data.workspaceId,
            data: { queueName, jobType: job.data.type }
          });

          const result = await processor(job);

          // Emit job completed event
          await this.emitEvent({
            type: 'job.completed',
            jobId: job.data.id,
            correlationId: job.data.correlationId,
            workspaceId: job.data.workspaceId,
            data: { result, queueName, jobType: job.data.type }
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          // Emit job failed event
          await this.emitEvent({
            type: 'job.failed',
            jobId: job.data.id,
            correlationId: job.data.correlationId,
            workspaceId: job.data.workspaceId,
            error: errorMessage,
            data: { queueName, jobType: job.data.type }
          });

          throw error;
        }
      },
      {
        connection: this.redis,
        concurrency: 5,
        removeOnComplete: 100,
        removeOnFail: 50
      }
    );

    // Handle worker events
    worker.on('failed', async (job, err) => {
      if (job) {
        await this.emitEvent({
          type: 'job.failed',
          jobId: job.data.id,
          correlationId: job.data.correlationId,
          workspaceId: job.data.workspaceId,
          error: err.message,
          data: { queueName, jobType: job.data.type, attemptsMade: job.attemptsMade }
        });
      }
    });

    this.workers.set(queueName, worker);
    return worker;
  }

  setupQueueEvents(queueName: string): QueueEvents {
    if (this.queueEvents.has(queueName)) {
      return this.queueEvents.get(queueName)!;
    }

    const queueEvents = new QueueEvents(queueName, {
      connection: this.redis
    });

    // Listen to queue events
    queueEvents.on('progress', async ({ jobId, data }) => {
      const job = await this.getQueue(queueName).getJob(jobId);
      if (job) {
        await this.emitEvent({
          type: 'job.progress',
          jobId: job.data.id,
          correlationId: job.data.correlationId,
          workspaceId: job.data.workspaceId,
          data: { progress: data, queueName, jobType: job.data.type }
        });
      }
    });

    this.queueEvents.set(queueName, queueEvents);
    return queueEvents;
  }

  onEvent(eventType: EventType, handler: (event: EventData) => void): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  private async emitEvent(event: EventData): Promise<void> {
    const handlers = this.eventHandlers.get(event.type) || [];
    
    // Execute all handlers
    await Promise.all(
      handlers.map(handler => {
        try {
          return handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${event.type}:`, error);
        }
      })
    );

    // Store event in Redis for observability
    await this.redis.lpush(
      `events:${event.workspaceId}`,
      JSON.stringify(event)
    );

    // Keep only last 1000 events per workspace
    await this.redis.ltrim(`events:${event.workspaceId}`, 0, 999);
  }

  async getEvents(workspaceId: number, limit: number = 100): Promise<EventData[]> {
    const events = await this.redis.lrange(`events:${workspaceId}`, 0, limit - 1);
    return events.map(event => JSON.parse(event));
  }

  async getJobStatus(queueName: string, jobId: string): Promise<JobStatus | null> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);
    
    if (!job) return null;

    const state = await job.getState();
    
    switch (state) {
      case 'waiting':
      case 'delayed':
        return 'pending';
      case 'active':
        return 'processing';
      case 'completed':
        return 'completed';
      case 'failed':
        return job.attemptsMade < (job.opts.attempts || 1) ? 'retrying' : 'failed';
      default:
        return 'pending';
    }
  }

  async retryJob(queueName: string, jobId: string): Promise<void> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);
    
    if (job) {
      await job.retry();
      
      await this.emitEvent({
        type: 'job.retrying',
        jobId: job.data.id,
        correlationId: job.data.correlationId,
        workspaceId: job.data.workspaceId,
        data: { queueName, jobType: job.data.type, attemptsMade: job.attemptsMade }
      });
    }
  }

  async cancelJob(queueName: string, jobId: string): Promise<void> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);
    
    if (job) {
      await job.remove();
    }
  }

  async getQueueStats(queueName: string) {
    const queue = this.getQueue(queueName);
    
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed()
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      total: waiting.length + active.length + completed.length + failed.length + delayed.length
    };
  }
}

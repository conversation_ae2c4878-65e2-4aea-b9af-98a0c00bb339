import { Job } from 'bullmq';
import { QueueClient } from './client.js';
import { 
  JobData, 
  JobResult, 
  TTSJobData, 
  LipSyncJobData, 
  CompositionJobData,
  UploadJobData,
  NotificationJobData
} from './types.js';
import { ttsAdapter, lipSyncAdapter, compositionAdapter } from './providers/index.js';

export class WorkerManager {
  constructor(private queueClient: QueueClient) {}

  async startAllWorkers(): Promise<void> {
    console.log('Starting all workers...');

    // Start TTS worker
    this.queueClient.createWorker('tts', this.processTTSJob.bind(this));
    console.log('TTS worker started');

    // Start LipSync worker
    this.queueClient.createWorker('lipsync', this.processLipSyncJob.bind(this));
    console.log('LipSync worker started');

    // Start Composition worker
    this.queueClient.createWorker('composition', this.processCompositionJob.bind(this));
    console.log('Composition worker started');

    // Start Upload worker
    this.queueClient.createWorker('upload', this.processUploadJob.bind(this));
    console.log('Upload worker started');

    // Start Notification worker
    this.queueClient.createWorker('notification', this.processNotificationJob.bind(this));
    console.log('Notification worker started');

    console.log('All workers started successfully');
  }

  private async processTTSJob(job: Job<JobData>): Promise<JobResult> {
    const jobData = job.data as TTSJobData;
    console.log(`Processing TTS job ${jobData.id} for workspace ${jobData.workspaceId}`);

    try {
      // Update progress
      await job.updateProgress(10);

      // Validate job data
      if (!jobData.text || jobData.text.trim().length === 0) {
        throw new Error('Text is required for TTS generation');
      }

      if (!jobData.voiceId) {
        throw new Error('Voice ID is required for TTS generation');
      }

      await job.updateProgress(25);

      // Process with TTS adapter
      const result = await ttsAdapter.processJob(jobData);

      if (!result.success) {
        throw new Error(result.error || 'TTS generation failed');
      }

      await job.updateProgress(90);

      // TODO: Save TTS output to database
      console.log(`TTS job ${jobData.id} completed successfully`);
      console.log(`Audio URL: ${result.outputUrl}`);

      await job.updateProgress(100);

      return result;
    } catch (error) {
      console.error(`TTS job ${jobData.id} failed:`, error);
      throw error;
    }
  }

  private async processLipSyncJob(job: Job<JobData>): Promise<JobResult> {
    const jobData = job.data as LipSyncJobData;
    console.log(`Processing LipSync job ${jobData.id} for workspace ${jobData.workspaceId}`);

    try {
      await job.updateProgress(10);

      // Validate job data
      const validation = await lipSyncAdapter.validateJobData(jobData);
      if (!validation.valid) {
        throw new Error(`Invalid job data: ${validation.errors.join(', ')}`);
      }

      await job.updateProgress(25);

      // Process with LipSync adapter
      const result = await lipSyncAdapter.processJob(jobData);

      if (!result.success) {
        throw new Error(result.error || 'LipSync generation failed');
      }

      await job.updateProgress(90);

      // TODO: Save LipSync output to database
      console.log(`LipSync job ${jobData.id} completed successfully`);
      console.log(`Video URL: ${result.outputUrl}`);

      await job.updateProgress(100);

      return result;
    } catch (error) {
      console.error(`LipSync job ${jobData.id} failed:`, error);
      throw error;
    }
  }

  private async processCompositionJob(job: Job<JobData>): Promise<JobResult> {
    const jobData = job.data as CompositionJobData;
    console.log(`Processing Composition job ${jobData.id} for workspace ${jobData.workspaceId}`);

    try {
      await job.updateProgress(10);

      // Validate job data
      const validation = await compositionAdapter.validateJobData(jobData);
      if (!validation.valid) {
        throw new Error(`Invalid job data: ${validation.errors.join(', ')}`);
      }

      await job.updateProgress(25);

      // Process with Composition adapter
      const result = await compositionAdapter.processJob(jobData);

      if (!result.success) {
        throw new Error(result.error || 'Video composition failed');
      }

      await job.updateProgress(90);

      // TODO: Save Composition output to database
      console.log(`Composition job ${jobData.id} completed successfully`);
      console.log(`Video URL: ${result.outputUrl}`);

      await job.updateProgress(100);

      return result;
    } catch (error) {
      console.error(`Composition job ${jobData.id} failed:`, error);
      throw error;
    }
  }

  private async processUploadJob(job: Job<JobData>): Promise<JobResult> {
    const jobData = job.data as UploadJobData;
    console.log(`Processing Upload job ${jobData.id} for workspace ${jobData.workspaceId}`);

    try {
      await job.updateProgress(10);

      // Validate job data
      if (!jobData.videoUrl || !jobData.videoUrl.startsWith('http')) {
        throw new Error('Valid video URL is required');
      }

      if (!jobData.platforms || jobData.platforms.length === 0) {
        throw new Error('At least one platform is required');
      }

      if (!jobData.caption || jobData.caption.trim().length === 0) {
        throw new Error('Caption is required');
      }

      await job.updateProgress(25);

      // Simulate upload to each platform
      const uploadResults: Record<string, any> = {};

      for (let i = 0; i < jobData.platforms.length; i++) {
        const platform = jobData.platforms[i];
        console.log(`Uploading to ${platform}...`);

        // Simulate platform-specific upload
        await new Promise(resolve => setTimeout(resolve, 2000));

        uploadResults[platform] = {
          success: true,
          postId: `${platform}_${Date.now()}`,
          url: `https://${platform}.com/post/${Date.now()}`,
          uploadedAt: new Date().toISOString()
        };

        await job.updateProgress(25 + (i + 1) * (65 / jobData.platforms.length));
      }

      await job.updateProgress(90);

      console.log(`Upload job ${jobData.id} completed successfully`);
      console.log(`Uploaded to platforms:`, Object.keys(uploadResults));

      await job.updateProgress(100);

      return {
        success: true,
        data: uploadResults,
        metadata: {
          platforms: jobData.platforms,
          caption: jobData.caption,
          hashtags: jobData.hashtags,
          scheduledAt: jobData.scheduledAt
        }
      };
    } catch (error) {
      console.error(`Upload job ${jobData.id} failed:`, error);
      throw error;
    }
  }

  private async processNotificationJob(job: Job<JobData>): Promise<JobResult> {
    const jobData = job.data as NotificationJobData;
    console.log(`Processing Notification job ${jobData.id} for workspace ${jobData.workspaceId}`);

    try {
      await job.updateProgress(25);

      // Validate job data
      if (!jobData.channel) {
        throw new Error('Notification channel is required');
      }

      if (!jobData.template) {
        throw new Error('Notification template is required');
      }

      await job.updateProgress(50);

      // Simulate sending notification
      console.log(`Sending ${jobData.channel} notification using template ${jobData.template}`);
      console.log(`Notification data:`, jobData.data);

      // Simulate notification delivery
      await new Promise(resolve => setTimeout(resolve, 1000));

      await job.updateProgress(90);

      const notificationResult = {
        channel: jobData.channel,
        template: jobData.template,
        sentAt: new Date().toISOString(),
        messageId: `msg_${Date.now()}`,
        status: 'delivered'
      };

      console.log(`Notification job ${jobData.id} completed successfully`);
      console.log(`Message ID: ${notificationResult.messageId}`);

      await job.updateProgress(100);

      return {
        success: true,
        data: notificationResult,
        metadata: {
          channel: jobData.channel,
          template: jobData.template,
          userId: jobData.userId
        }
      };
    } catch (error) {
      console.error(`Notification job ${jobData.id} failed:`, error);
      throw error;
    }
  }

  async getWorkerStats(): Promise<Record<string, any>> {
    const queueNames = ['tts', 'lipsync', 'composition', 'upload', 'notification'];
    const stats: Record<string, any> = {};

    for (const queueName of queueNames) {
      try {
        stats[queueName] = await this.queueClient.getQueueStats(queueName);
      } catch (error) {
        console.error(`Failed to get stats for queue ${queueName}:`, error);
        stats[queueName] = { error: 'Failed to get stats' };
      }
    }

    return stats;
  }
}

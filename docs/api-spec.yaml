openapi: 3.0.3
info:
  title: UGC Orchestrator API
  description: |
    API for the UGC (User Generated Content) Orchestrator platform.
    This API enables AI-powered campaign creation, content brief generation, and approval workflows.
  version: 1.0.0
  contact:
    name: UGC Orchestrator Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3002/api
    description: Local development server
  - url: https://api.ugcorchestrator.com
    description: Production server

paths:
  /campaigns:
    post:
      summary: Create a new campaign
      description: |
        Creates a new UGC campaign with AI-powered strategy generation.
        Accepts natural language descriptions and generates structured campaign data.
      operationId: createCampaign
      tags:
        - Campaigns
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCampaignRequest'
            examples:
              basic:
                summary: Basic campaign creation
                value:
                  name: "Summer Product Launch"
                  workspaceSlug: "acme"
                  objective: "awareness"
              ai_generated:
                summary: AI-generated campaign
                value:
                  name: "AI Generated Campaign"
                  workspaceSlug: "acme"
                  objective: "conversion"
                  prompt: "I want to launch a summer campaign for our new skincare line targeting young women..."
      responses:
        '201':
          description: Campaign created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Campaign'
        '400':
          description: Bad request - invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /briefs:
    post:
      summary: Generate a content brief
      description: |
        Generates AI-powered content briefs based on natural language prompts.
        Creates detailed scripts, angles, and captions for content creators.
      operationId: generateBrief
      tags:
        - Briefs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateBriefRequest'
            examples:
              tiktok_skincare:
                summary: TikTok skincare routine
                value:
                  campaignId: 1
                  title: "Morning Skincare Routine"
                  prompt: "Create a TikTok video showing a morning skincare routine with our new serum..."
              instagram_unboxing:
                summary: Instagram unboxing
                value:
                  campaignId: 1
                  title: "Product Unboxing"
                  prompt: "Need an Instagram Reel of someone unboxing our fitness equipment..."
      responses:
        '201':
          description: Brief generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brief'
        '400':
          description: Bad request - invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Campaign not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /briefs/{id}/approve:
    post:
      summary: Approve or reject a brief
      description: |
        Records approval or rejection status for a generated brief.
        Includes optional notes and tracks the deciding user.
      operationId: approveBrief
      tags:
        - Briefs
      parameters:
        - name: id
          in: path
          required: true
          description: Brief ID
          schema:
            type: integer
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApproveBriefRequest'
            examples:
              approve:
                summary: Approve brief
                value:
                  status: "approved"
                  note: "Looks great, ready for production"
                  userId: 1
              reject:
                summary: Reject brief
                value:
                  status: "rejected"
                  note: "Needs more focus on product benefits"
                  userId: 1
      responses:
        '200':
          description: Approval status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Approval'
        '400':
          description: Bad request - invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Brief not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /events/stream:
    get:
      summary: Server-Sent Events stream
      description: |
        Establishes a Server-Sent Events connection for real-time updates.
        Provides heartbeat and event notifications for brief generation and approvals.
      operationId: getEventStream
      tags:
        - Events
      responses:
        '200':
          description: SSE stream established
          content:
            text/event-stream:
              schema:
                type: string
                description: Server-sent events stream
              examples:
                hello:
                  summary: Initial connection
                  value: |
                    event: hello
                    data: {"message": "Connected to UGC Orchestrator events"}
                    
                heartbeat:
                  summary: Periodic heartbeat
                  value: |
                    event: heartbeat
                    data: {"timestamp": "2024-01-01T12:00:00Z"}

components:
  schemas:
    CreateCampaignRequest:
      type: object
      required:
        - name
        - workspaceSlug
      properties:
        name:
          type: string
          maxLength: 160
          description: Campaign name
          example: "Summer Product Launch"
        workspaceSlug:
          type: string
          maxLength: 120
          description: Workspace identifier
          example: "acme"
        objective:
          type: string
          enum: [awareness, conversion, engagement]
          description: Campaign objective
          example: "awareness"
        prompt:
          type: string
          description: Natural language description for AI generation
          example: "I want to launch a summer campaign for our new skincare line..."

    GenerateBriefRequest:
      type: object
      required:
        - campaignId
        - title
      properties:
        campaignId:
          type: integer
          description: ID of the campaign this brief belongs to
          example: 1
        title:
          type: string
          maxLength: 200
          description: Brief title
          example: "Morning Skincare Routine"
        prompt:
          type: string
          description: Natural language description of desired content
          example: "Create a TikTok video showing a morning skincare routine..."

    ApproveBriefRequest:
      type: object
      required:
        - status
        - userId
      properties:
        status:
          type: string
          enum: [approved, rejected]
          description: Approval status
          example: "approved"
        note:
          type: string
          description: Optional approval note
          example: "Looks great, ready for production"
        userId:
          type: integer
          description: ID of the user making the decision
          example: 1

    Campaign:
      type: object
      properties:
        id:
          type: integer
          description: Campaign ID
          example: 1
        workspaceId:
          type: integer
          description: Workspace ID
          example: 1
        name:
          type: string
          description: Campaign name
          example: "Summer Product Launch"
        objective:
          type: string
          enum: [awareness, conversion, engagement]
          description: Campaign objective
          example: "awareness"
        status:
          type: string
          enum: [draft, active, paused, archived]
          description: Campaign status
          example: "draft"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-01T12:00:00Z"

    Brief:
      type: object
      properties:
        id:
          type: integer
          description: Brief ID
          example: 1
        campaignId:
          type: integer
          description: Campaign ID
          example: 1
        title:
          type: string
          description: Brief title
          example: "Morning Skincare Routine"
        content:
          $ref: '#/components/schemas/BriefContent'
        generatedBy:
          type: string
          enum: [user, agent]
          description: Generation source
          example: "agent"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-01T12:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-01T12:00:00Z"

    BriefContent:
      type: object
      description: AI-generated content structure
      properties:
        angles:
          type: array
          description: Content angles and hooks
          items:
            type: object
            properties:
              id:
                type: string
                example: "a1"
              hook:
                type: string
                example: "Problem -> Solution"
              promise:
                type: string
                example: "Show how product solves X"
        scripts:
          type: array
          description: Content scripts and dialogue
          items:
            type: object
            properties:
              id:
                type: string
                example: "s1"
              angleId:
                type: string
                example: "a1"
              lines:
                type: array
                items:
                  type: string
                example: ["Hook line...", "Body line...", "CTA line..."]
        captions:
          type: array
          description: Social media captions
          items:
            type: object
            properties:
              id:
                type: string
                example: "c1"
              text:
                type: string
                example: "Try it today 🚀"
              platform:
                type: string
                example: "tiktok"

    Approval:
      type: object
      properties:
        id:
          type: integer
          description: Approval ID
          example: 1
        entityType:
          type: string
          enum: [brief, render, composition]
          description: Type of entity being approved
          example: "brief"
        entityId:
          type: integer
          description: ID of the entity being approved
          example: 1
        status:
          type: string
          enum: [pending, approved, rejected]
          description: Approval status
          example: "approved"
        note:
          type: string
          description: Approval note
          example: "Looks great, ready for production"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-01T12:00:00Z"
        decidedAt:
          type: string
          format: date-time
          description: Decision timestamp
          example: "2024-01-01T12:00:00Z"
        decidedByUserId:
          type: integer
          description: ID of user who made the decision
          example: 1

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Campaign not found"
        code:
          type: string
          description: Error code
          example: "CAMPAIGN_NOT_FOUND"
        details:
          type: object
          description: Additional error details
          additionalProperties: true

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

security:
  - bearerAuth: []

tags:
  - name: Campaigns
    description: Campaign management and AI-powered creation
  - name: Briefs
    description: Content brief generation and approval
  - name: Events
    description: Real-time event streaming

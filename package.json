{"name": "ugc-gen", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace @ugc/web", "dev:web": "npm run dev --workspace @ugc/web", "build": "npm run build --workspaces", "start": "npm run start --workspace @ugc/web", "lint": "eslint . --fix", "lint:check": "eslint .", "lint:workspaces": "npm run lint --workspaces", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "npm run typecheck --workspaces", "typecheck:web": "npm run typecheck --workspace @ugc/web", "db:generate": "drizzle-kit generate --config packages/db/drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config packages/db/drizzle.config.ts", "db:seed": "tsx --env-file=.env packages/db/src/seed.ts", "seed": "tsx --env-file=.env packages/db/src/seed.ts"}, "dependencies": {"next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-plugin-import": "^2.31.0", "prettier": "^3.4.2", "tailwindcss": "^4", "typescript": "^5"}, "engines": {"node": ">=20"}}